import { fileURLToPath, URL } from 'node:url'
import path from 'path'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import { compression } from 'vite-plugin-compression2'
import cssnano from 'cssnano'
import autoprefixer from 'autoprefixer'

// ===== 配置常量 =====
const IS_PRODUCTION = (mode) => mode === 'production' || mode === 'test'
const IS_DEVELOPMENT = (mode) => mode === 'development'

// ===== 插件配置函数 =====
/**
 * 创建自动导入插件配置
 */
const createAutoImportPlugin = (mode) => {
    return AutoImport({
        imports: ['vue', 'vue-router', 'pinia'],
        dts: false, // 关闭声明文件生成
        resolvers: [
            ElementPlusResolver({
                importStyle: 'sass', // 按需导入样式，使用 sass
            }),
        ],
        eslintrc: {
            enabled: IS_DEVELOPMENT(mode), // 只在开发环境生成
            filepath: './.eslintrc-auto-import.json',
            globalsPropValue: true,
        },
    })
}

/**
 * 创建组件自动导入插件配置
 */
const createComponentsPlugin = (mode) => {
    return Components({
        debug: IS_DEVELOPMENT(mode), // 只在开发环境启用调试
        dirs: ['src/components'],
        extensions: ['vue'],
        deep: true,
        dts: false,
        resolvers: [
            ElementPlusResolver({
                importStyle: 'sass',
            }),
        ],
        directoryAsNamespace: false,
        globalNamespaces: ['global'],
    })
}

/**
 * 创建 SVG 图标插件配置
 */
const createSvgPlugin = () => {
    return createSvgIconsPlugin({
        iconDirs: [path.resolve(process.cwd(), 'src/icons')],
        symbolId: 'icon-[name]',
        customDomId: '__svg__icons__dom__',
    })
}

/**
 * 创建压缩插件配置
 */
const createCompressionPlugin = () => {
    return compression({
        algorithm: 'gzip',
        exclude: [/\.(br)$/, /\.(gz)$/],
    })
}

/**
 * 获取所有插件配置
 */
const getPlugins = (mode) => {
    const plugins = [
        vue(),
        createAutoImportPlugin(mode),
        createComponentsPlugin(mode),
        createSvgPlugin(),
    ]

    // 只在开发环境启用 devtools
    if (IS_DEVELOPMENT(mode)) {
        plugins.push(vueDevTools())
    }

    // 生产和测试环境添加压缩插件
    if (IS_PRODUCTION(mode)) {
        plugins.push(createCompressionPlugin())
    }

    return plugins
}

// ===== CSS 配置函数 =====
/**
 * 获取 CSS 配置
 */
const getCssConfig = (mode) => {
    const config = {
        preprocessorOptions: {
            scss: {
                // 只导入 Element Plus 的 scss 变量，不导入完整样式
                additionalData: `@use "element-plus/theme-chalk/src/common/var.scss" as *;`,
                charset: false,
            },
        },
        // CSS 代码分割
        codeSplit: true,
    }

    // 生产环境添加 CSS 压缩
    if (IS_PRODUCTION(mode)) {
        config.postcss = {
            plugins: [
                autoprefixer(),
                cssnano({
                    preset: 'default',
                }),
            ],
        }
    }

    return config
}

// ===== 构建配置函数 =====
/**
 * 获取资源文件名配置
 */
const getAssetFileNames = (assetInfo) => {
    const info = assetInfo.name.split('.')
    let extType = info[info.length - 1]

    if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name)) {
        extType = 'media'
    } else if (/\.(png|jpe?g|gif|svg)(\?.*)?$/.test(assetInfo.name)) {
        extType = 'img'
    } else if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name)) {
        extType = 'fonts'
    }

    return `${extType}/[name]-[hash].[ext]`
}

/**
 * 获取构建配置
 */
const getBuildConfig = (mode) => {
    return {
        target: 'es2015',
        outDir: 'dist',
        assetsDir: 'assets',
        sourcemap: IS_DEVELOPMENT(mode),
        minify: 'terser',
        terserOptions: {
            compress: {
                drop_console: false,
                drop_debugger: false,
            },
        },
        rollupOptions: {
            output: {
                // 分包策略
                manualChunks: {
                    // Vue 核心
                    vue: ['vue', 'vue-router', 'pinia'],
                    // Element Plus
                    'element-plus': ['element-plus', '@element-plus/icons-vue'],
                    // 工具库
                    utils: ['axios', 'qs'],
                },
                // 文件命名
                chunkFileNames: 'js/[name]-[hash].js',
                entryFileNames: 'js/[name]-[hash].js',
                assetFileNames: getAssetFileNames,
            },
        },
        // 压缩配置
        chunkSizeWarningLimit: 1000,
        reportCompressedSize: false,
    }
}

// ===== 服务器配置函数 =====
/**
 * 获取开发服务器配置
 */
const getServerConfig = (env) => {
    return {
        host: '0.0.0.0',
        port: 1010,
        open: true,
        proxy: {
            '/api': {
                target: env.VITE_ADMIN_PROXY_PATH,
                ws: false,
                changeOrigin: true,
                rewrite: (path) => path.replace(/^\/api/, ''),
            },
        },
    }
}

// ===== 依赖优化配置 =====
/**
 * 获取依赖预构建配置
 */
const getOptimizeDepsConfig = () => {
    return {
        include: [
            'vue',
            'vue-router',
            'pinia',
            'element-plus/es',
            'element-plus/es/components/button/style/css',
            'element-plus/es/components/input/style/css',
            'element-plus/es/components/form/style/css',
            'element-plus/es/components/form-item/style/css',
            'element-plus/es/components/message/style/css',
            'element-plus/es/components/loading/style/css',
            'element-plus/es/components/notification/style/css',
            '@element-plus/icons-vue',
            'axios',
        ],
    }
}

// ===== 主配置导出 =====
export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, process.cwd(), '')
    console.log('当前 mode:', mode) // 打印 mode

    return {
        plugins: getPlugins(mode),
        base: mode.command === 'serve' ? './' : env.VITE_PUBLIC_PATH,
        resolve: {
            alias: {
                '@': fileURLToPath(new URL('./src', import.meta.url)),
            },
        },
        css: getCssConfig(mode),
        build: getBuildConfig(mode),
        server: getServerConfig(env),
        optimizeDeps: getOptimizeDepsConfig(),
    }
})
