# 网站主标题
VITE_GLOBAL_TITLE= 'Wo Agent'

# footer
VITE_FOOTER_TITLE= '©2024 VeLora.com'

# 是否是微服务架构（重要）
VITE_IS_MICRO= true

# 前端访问前缀
VITE_PUBLIC_PATH = /

# 后端请求前缀
VITE_API_URL = /api

# ADMIN 服务地址
#VITE_ADMIN_PROXY_PATH = http://localhost:9999

# 代码生成服务地址 (单体架构有效)
VITE_GEN_PROXY_PATH = http://localhost:5003

# 前端加密密钥
VITE_PWD_ENC_KEY='wyaiwyaiwyaiwyai'

# OAUTH2 密码模式客户端信息
VITE_OAUTH2_PASSWORD_CLIENT='client:client'

# OAUTH2 短信客户端信息
VITE_OAUTH2_SMS_CLIENT='client:client'

# OAUTH2 社交登录客户端信息
VITE_OAUTH2_SOCIAL_CLIENT='client:client'

# 是否开启前端滑块验证码
VITE_VERIFY_ENABLE = true

# 是否开启前端图形验证码
VITE_VERIFY_IMAGE_ENABLE = false

# 是否开启websocket 消息接受,
VITE_WEBSOCKET_ENABLE = false

# 是否开启注册
VITE_REGISTER_ENABLE  = false

# 是否开启租户自动选择 （根据租户域名）
VITE_AUTO_TENANT = false

# 上传图片大小,单位M
VITE_IMAGE_SIZE = '5'

# 上传视频大小,单位M
VITE_VIDEO_SIZE = '200'

# 上传音频大小,单位M
VITE_AUDIO_SIZE = '500'
