import { fileURLToPath, URL } from 'node:url'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import { compression } from 'vite-plugin-compression2'
import cssnano from 'cssnano'
import autoprefixer from 'autoprefixer'
import path from 'path'

export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, process.cwd(), '')
    console.log('当前 mode:', mode) // 打印 mode
    return {
        plugins: [
            vue(),
            // 只在开发环境启用 devtools
            mode === 'development' && vueDevTools(),

            // 自动导入 Vue/VueRouter/Pinia 等 API
            AutoImport({
                imports: ['vue', 'vue-router', 'pinia'],
                dts: false, // 关闭声明文件生成
                resolvers: [
                    ElementPlusResolver({
                        importStyle: 'sass', // 按需导入样式，使用 sass
                    }),
                ],
                eslintrc: {
                    enabled: mode === 'development', // 只在开发环境生成
                    filepath: './.eslintrc-auto-import.json',
                    globalsPropValue: true,
                },
            }),

            // 自动导入组件
            Components({
                debug: mode === 'development', // 只在开发环境启用调试
                dirs: ['src/components'],
                extensions: ['vue'],
                deep: true,
                dts: false,
                resolvers: [
                    ElementPlusResolver({
                        importStyle: 'sass',
                    }),
                ],
                directoryAsNamespace: false,
                globalNamespaces: ['global'],
            }),

            // SVG 图标插件
            createSvgIconsPlugin({
                iconDirs: [path.resolve(process.cwd(), 'src/icons')],
                symbolId: 'icon-[name]',
                customDomId: '__svg__icons__dom__',
            }),

            // 生产和测试环境优化插件
            (mode === 'production' || mode === 'test') &&
            compression({
                algorithm: 'gzip',
                exclude: [/\.(br)$ /, /\.(gz)$/],
            }),
        ].filter(Boolean),
        base: mode.command === 'serve' ? './' : env.VITE_PUBLIC_PATH,
        resolve: {
            alias: {
                '@': fileURLToPath(new URL('./src', import.meta.url)),
            },
        },
        css: {
            preprocessorOptions: {
                scss: {
                    // 只导入 Element Plus 的 scss 变量，不导入完整样式
                    additionalData: `@use "element-plus/theme-chalk/src/common/var.scss" as *;`,
                    charset: false,
                },
            },
            // CSS 代码分割
            codeSplit: true,
            // 生产环境 CSS 压缩
            ...((mode === 'production' || mode === 'test') && {
                postcss: {
                    plugins: [
                        autoprefixer(),
                        cssnano({
                            preset: 'default',
                        }),
                    ],
                },
            }),
        },
        // 构建优化
        build: {
            target: 'es2015',
            outDir: 'dist',
            assetsDir: 'assets',
            sourcemap: mode === 'development',
            minify: 'terser',
            terserOptions: {
                compress: {
                    drop_console: true,
                    drop_debugger: true,
                },
            },
            rollupOptions: {
                output: {
                    // 分包策略
                    manualChunks: {
                        // Vue 核心
                        vue: ['vue', 'vue-router', 'pinia'],
                        // Element Plus
                        'element-plus': ['element-plus', '@element-plus/icons-vue'],
                        // 工具库
                        utils: ['axios', 'qs'],
                    },
                    // 文件命名
                    chunkFileNames: 'js/[name]-[hash].js',
                    entryFileNames: 'js/[name]-[hash].js',
                    assetFileNames: (assetInfo) => {
                        const info = assetInfo.name.split('.')
                        let extType = info[info.length - 1]
                        if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name)) {
                            extType = 'media'
                        } else if (/\.(png|jpe?g|gif|svg)(\?.*)?$/.test(assetInfo.name)) {
                            extType = 'img'
                        } else if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name)) {
                            extType = 'fonts'
                        }
                        return `${extType}/[name]-[hash].[ext]`
                    },
                },
            },
            // 压缩配置
            chunkSizeWarningLimit: 1000,
            reportCompressedSize: false,
        },

        server: {
            host: '0.0.0.0',
            port: 1010,
            open: true,
            proxy: {
                '/api': {
                    target: env.VITE_ADMIN_PROXY_PATH,
                    ws: false,
                    changeOrigin: true,
                    rewrite: (path) => path.replace(/^\/api/, ''),
                },
            },
        },

        // 预构建优化
        optimizeDeps: {
            include: [
                'vue',
                'vue-router',
                'pinia',
                'element-plus/es',
                'element-plus/es/components/button/style/css',
                'element-plus/es/components/input/style/css',
                'element-plus/es/components/form/style/css',
                'element-plus/es/components/form-item/style/css',
                'element-plus/es/components/message/style/css',
                'element-plus/es/components/loading/style/css',
                'element-plus/es/components/notification/style/css',
                '@element-plus/icons-vue',
                'axios',
            ],
        },
    }
})
