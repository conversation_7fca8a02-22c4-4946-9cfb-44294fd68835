{"name": "woagent-client", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:test": "vite build --mode test", "build:prod": "vite build --mode production", "preview": "vite preview", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.10.0", "element-plus": "^2.10.2", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.3.0", "qrcode": "^1.5.4", "qs": "^6.14.0", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@eslint/js": "^9.29.0", "@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-prettier": "^10.2.0", "@vue/runtime-core": "^3.5.17", "autoprefixer": "^10.4.20", "cssnano": "^7.0.6", "eslint": "^9.29.0", "eslint-plugin-vue": "~10.2.0", "fast-glob": "^3.3.3", "globals": "^16.2.0", "postcss": "^8.5.6", "prettier": "3.5.3", "sass": "^1.89.2", "terser": "^5.36.0", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vite": "^7.0.0", "vite-plugin-compression2": "^1.3.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.7.7"}, "engines": {"node": ">=20.0.0", "pnpm": ">=8.0.0"}}