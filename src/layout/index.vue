<template>
    <el-container direction="vertical">
        <woagent-header></woagent-header>
        <el-container>
            <woagent-aside></woagent-aside>
            <el-main>
                <el-scrollbar
                    ref="mainScrollbarRef"
                    class="main-scrollbar hide-scrollbar"
                    @scroll="handleScroll">
                    <router-view v-slot="{ Component, route }">
                        <keep-alive>
                            <component :is="Component" v-if="route.meta?.keepAlive" :key="route.fullPath" />
                        </keep-alive>
                        <component :is="Component" v-if="!route.meta?.keepAlive" :key="route.fullPath" />
                    </router-view>
                </el-scrollbar>
            </el-main>
        </el-container>
    </el-container>
</template>
<script setup>
import WoagentHeader from '@/layout/components/WoagentHeader.vue'
import WoagentAside from '@/layout/components/WoagentAside.vue'
import { useScrollbarStore } from '@/stores/scrollbar.js'

const scrollbarStore = useScrollbarStore()
const mainScrollbarRef = ref(null)

// 滚动事件处理
const handleScroll = ({ scrollTop, scrollLeft }) => {
    if (!mainScrollbarRef.value) return

    // 获取滚动容器的尺寸信息
    const scrollbarWrap = mainScrollbarRef.value.wrapRef
    if (!scrollbarWrap) return

    const scrollHeight = scrollbarWrap.scrollHeight
    const clientHeight = scrollbarWrap.clientHeight

    // 更新 store 中的滚动状态
    scrollbarStore.updateScrollState({
        scrollTop,
        scrollHeight,
        clientHeight
    })
}



// 备用方案：直接监听 DOM 滚动事件
let domScrollCleanup = null

onMounted(() => {
    nextTick(() => {
        if (mainScrollbarRef.value) {
            const wrapEl = mainScrollbarRef.value.wrapRef
            if (wrapEl) {
                // 直接监听 DOM 滚动事件作为备用方案
                const handleDOMScroll = () => {
                    const scrollTop = wrapEl.scrollTop
                    const scrollHeight = wrapEl.scrollHeight
                    const clientHeight = wrapEl.clientHeight

                    scrollbarStore.updateScrollState({
                        scrollTop,
                        scrollHeight,
                        clientHeight
                    })
                }

                wrapEl.addEventListener('scroll', handleDOMScroll)

                // 保存清理函数
                domScrollCleanup = () => {
                    wrapEl.removeEventListener('scroll', handleDOMScroll)
                }
            }
        }
    })
})

// 组件卸载时清理
onUnmounted(() => {
    if (domScrollCleanup) {
        domScrollCleanup()
        domScrollCleanup = null
    }
})
</script>

<style scoped lang="scss">
.el-container {
    height: 100vh;
}

.el-main {
    padding: 0;
    overflow: hidden;
}

.main-scrollbar {
    width: 100%;
    height: calc(100vh - 60px);
}
</style>
