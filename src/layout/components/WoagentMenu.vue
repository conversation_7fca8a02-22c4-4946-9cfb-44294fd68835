<template>
    <el-menu class="woagent-menu" router :default-active="activeIndex">
        <template v-for="(menu,menuIndex) in menuList" :key="menuIndex">
            <el-menu-item-group>
                <template #title>
                    <div class="_group-title">{{ menu.meta.title }}</div>
                </template>
                <template v-for="(item,itemIndex) in menu.children" :key="itemIndex">
                    <el-menu-item :index="item.path">
                        <svg-icon :name=" item.meta.icon"></svg-icon>
                        <div class="_label">{{ item.meta.title }}</div>
                    </el-menu-item>
                </template>
            </el-menu-item-group>
        </template>
    </el-menu>
</template>

<script setup>
const menuList = [
    {
        path: '/trade',
        meta: { title: '服贸对接', icon: 'dashboard' },
        children: [
            {
                path: '/trade/good',
                name: 'GoodPage',
                meta: { title: '找产品', icon: 'zhaochanpin' }
            },
            {
                path: '/trade/service',
                name: 'ServicePage',
                meta: { title: '找服务', icon: 'zhaofuwu' }
            },
            {
                path: '/trade/supply-demand',
                name: 'SupplyDemand',
                meta: { title: '供需对接', icon: 'gongxuyuce' }
            }
        ]
    },
    {
        path: '/supply',
        meta: { title: '资源对接', icon: 'dashboard' },
        children: [
            {
                path: '/supply/association',
                name: 'Association',
                meta: { title: '商协会', icon: 'xiehuihuiyuan' }
            },
            {
                path: '/supply/company',
                name: 'Company',
                meta: { title: '企业目录', icon: 'qiyexinxi' }
            },
            {
                path: '/supply/procurement',
                name: 'Procurement',
                meta: { title: '采购人(央国企)', icon: 'caigouren' }
            },
            {
                path: '/supply/supplier',
                name: 'Supplier',
                meta: { title: '渠道商', icon: 'qudaoshang' }
            }
        ]
    },
    {
        path: '/mine',
        meta: { title: '我的', icon: 'dashboard' },
        children: [
            {
                path: '/mine/user-center',
                name: 'UserCenter',
                meta: { title: '个人中心', icon: 'user' }
            },
            {
                path: '/mine/order',
                name: 'Order',
                meta: { title: '订单管理', icon: 'dingdanguanli' }
            },
            {
                path: '/mine/address',
                name: 'Address',
                meta: { title: '地址管理', icon: 'location' }
            },
            // {
            //     path: '/mine/after-sale',
            //     name: 'AfterSale',
            //     meta: { title: '售后服务', icon: 'shouhoufuwu' }
            // },
            {
                path: '/mine/authentication',
                name: 'Authentication',
                meta: { title: '实名认证', icon: 'shimingrenzheng' }
            },
            // {
            //     path: '/mine/invoice',
            //     name: 'Invoice',
            //     meta: { title: '发票管理', icon: 'fapiaoguanli' }
            // }
        ]
    }
]

const route = useRoute()
const activeIndex = ref('')

// 或者只监听特定属性
watch(() => route.path, (newPath) => {
    activeIndex.value = newPath
}, { deep: true, immediate: true })
</script>

<style scoped lang="scss">
.woagent-menu {
    width: 100%;
    --el-menu-text-color: #000;
    --el-menu-active-color: #000;
    --el-menu-item-height: 46px;
    border-right: none;

    &.el-menu {
        border-right: none;
    }

    ._group-title {
        padding-top: 10px;
        padding-bottom: 5px;
    }

    ._label {
        margin-left: 6px;
    }

    .el-menu-item {
        margin: 0 10px 3px 10px;

        &:hover {
            background: #f4f5f9;
            border-radius: 12px;
        }

        &.is-active {
            background: #f4f5f9 url("@/assets/menu_bg.png") no-repeat left top -10px;
            background-size: 32px 76px;
            border-radius: 12px;

            ._label {
                font-weight: bolder;
            }
        }
    }
}
</style>
