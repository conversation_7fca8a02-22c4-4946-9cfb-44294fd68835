<template>
    <el-header class="woagent-header flex-row jc-space-between ai-center" height="60px">
        <div class="_item flex-row ai-center">
            <el-image class="logo" :src="logo"></el-image>
            <div class="title">东莞制造政企采购服务智能平台</div>
        </div>
        <div class="_item flex-row ai-center">
            <div class="_cart flex-row ai-center" @click="openCart">
                <svg-icon name="caigouren" size="12"></svg-icon>
                <div class="_label">
                    <div class="_span">购物车</div>
                    <div class="_num" v-if="cart.totalCart > 0 && userStore.token">
                        {{ cart.totalCart }}
                    </div>
                </div>
            </div>
            <div v-if="userStore.userInfo" class="_user-info">
                <el-dropdown @command="handleUserCommand">
                    <div class="user-dropdown flex-row ai-center">
                        <div class="_name">{{ userStore.userInfo.nickname }}</div>
                        <el-image class="_avatar" :src="userStore.userInfo.avatar"></el-image>
                        <el-icon class="dropdown-icon">
                            <arrow-down />
                        </el-icon>
                    </div>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item command="profile">
                                <el-icon>
                                    <user />
                                </el-icon>
                                个人中心
                            </el-dropdown-item>
                            <el-dropdown-item command="orders">
                                <el-icon>
                                    <document />
                                </el-icon>
                                我的订单
                            </el-dropdown-item>
                            <el-dropdown-item divided command="logout">
                                <el-icon>
                                    <switch-button />
                                </el-icon>
                                退出登录
                            </el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </div>
            <div v-else class="login-box flex-row ai-center">
                <div class="login-btn" @click="tapLogin">登录/注册</div>
            </div>
        </div>
    </el-header>

    <login-pop ref="loginPopRef"></login-pop>
    <shop-cart v-if="visible" ref="shopCartRef" @close="closeCart"></shop-cart>
</template>

<script setup>
import logo from '@/assets/logo.png'
import LoginPop from '@/components/LoginPop/LoginPop.vue'
import { useUserStore } from '@/stores/user'
import { useOtherStore } from '@/stores/other.js'
import SvgIcon from '@/components/SvgIcon/SvgIcon.vue'
import ShopCart from '@/components/ShopCart/ShopCart.vue'
import { useAuth } from '@/composables/useAuth.js'
import { ArrowDown, User, Document, SwitchButton } from '@element-plus/icons-vue'
import { useCartStore } from '@/stores/shop-cart.js'

const userStore = useUserStore()
const otherStore = useOtherStore()
const { navigateWithAuth, logout } = useAuth()

const loginPopRef = ref()
const shopCartRef = ref()
const visible = ref(false)
const { executeWithAuthAsync } = useAuth()

const cart = useCartStore()

const tapLogin = () => {
    loginPopRef.value.openDialog()
}

// 处理用户下拉菜单命令
const handleUserCommand = (command) => {
    switch (command) {
        case 'profile':
            navigateWithAuth('/mine/user-center')
            break
        case 'orders':
            navigateWithAuth('/mine/order')
            break
        case 'logout':
            logout()
            break
    }
}

/**
 * 打开购物车侧边栏
 *
 */
const openCart = () => {
    // 需要登录才能查看购物车
    executeWithAuthAsync(() => {
        visible.value = true // 控制购物车可见性的响应式变量
        nextTick(() => {
            shopCartRef.value.openDrawer() // 通过组件引用调用打开抽屉方法
        })
    }, '')
}

/**
 * 关闭购物车侧边栏
 *
 * 该函数执行以下操作：
 * 1. 将 visible 状态设置为 false，隐藏购物车组件
 */
const closeCart = () => {
    visible.value = false // 控制购物车可见性的响应式变量
}

onMounted(() => {
    // 加载购物车商品数量
    setTimeout(() => {
        if (userStore.token) {
            cart.getCount()
        }
    }, 1500)

    if (otherStore.regionList.length === 0) {
        otherStore.loadRegion()
    }
})
</script>

<style scoped lang="scss">
.woagent-header {
    width: 100%;
    border-bottom: 1px solid #e9ecef;

    ._item {
        height: 100%;
        gap: 15px;

        .logo {
            width: 100px;
        }

        .title {
            font-size: 18px;
            font-weight: 550;
            color: #303133;
        }

        ._user-info {
            .user-dropdown {
                gap: 10px;
                cursor: pointer;
                padding: 5px 10px;
                border-radius: 5px;
                transition: background-color 0.3s ease;

                &:hover {
                    background-color: rgba(255, 255, 255, 0.1);
                }

                ._avatar {
                    width: 40px;
                    height: 40px;
                }

                ._name {
                    font-size: 14px;
                    color: #626066;
                }

                .dropdown-icon {
                    color: #ffffff;
                    font-size: 12px;
                    margin-left: 5px;
                }
            }
        }

        .login-box {
            .login-btn {
                font-size: 14px;
                color: #ffffff;
                height: 30px;
                line-height: 30px;
                border-radius: 5px;
                padding: 0 15px;
                cursor: pointer;
                font-weight: 550;
                background-image: linear-gradient(90deg, #197dff, #2c4dff) !important;
            }
        }

        ._cart {
            color: #ff0f23;
            padding-left: 10px;
            gap: 5px;
            cursor: pointer;

            ._label {
                font-size: 12px;
                color: #939099;
                position: relative;

                ._num {
                    position: absolute;
                    top: -5px;
                    right: -15px;
                    width: 16px;
                    height: 16px;
                    line-height: 16px;
                    text-align: center;
                    background: #ff0f23;
                    color: #fff;
                    border-radius: 50%;
                    font-size: 12px;
                }
            }
        }
    }
}
</style>
