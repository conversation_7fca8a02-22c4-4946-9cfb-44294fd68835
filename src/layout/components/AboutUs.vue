<template>
    <div class="about-us flex-column">
        <div class="about-title flex-row jc-center ai-center">
            <svg-icon name="user" color="#333"></svg-icon>
            <div class="_label">加入我们</div>
        </div>

        <div class="about-tab flex-row jc-space-between ai-center">
            <div class="about-tab-item">用户协议</div>
            <div class="about-tab-item">隐私政策</div>
            <div class="about-tab-item">关于我们</div>
        </div>

        <div class="about-name">广东沃云网络科技有限公司</div>
        <div class="about-name">京ICP备2023015442号</div>
        <div class="about-name about-other">备案号:Beijing-PianYu-20240226</div>
    </div>
</template>

<script setup>
import SvgIcon from '@/components/SvgIcon/SvgIcon.vue'
</script>

<style scoped lang="scss">
.about-us {
    padding: 20px;
    gap: 10px;

    .about-title {
        width: 100%;
        height: 32px;
        border: 1px #ccc dashed;
        border-radius: 5px;
        gap: 5px;

        ._label {
            font-size: 13px;
            color: #333333;
        }
    }

    .about-tab {
        .about-tab-item {
            font-size: 12px;
            color: #999999;
            cursor: pointer;

            &:hover {
                color: #1a73e8;
                font-weight: 550;
            }
        }
    }

    .about-name {
        font-size: 12px;
        color: #999999;

        &.about-other {
            font-size: 12px;
        }
    }
}
</style>
