import axios from 'axios'
import qs from 'qs'
import { useUserStore } from '@/stores/user.js'
import { ElMessage } from 'element-plus'
import { useMessage } from '@/utils/useMessage.js'

/**
 * 创建并配置一个 Axios 实例对象
 */
const service = axios.create({
    baseURL: import.meta.env.VITE_API_URL,
    timeout: 50000, // 全局超时时间
    paramsSerializer: {
        serialize: (params) => {
            return qs.stringify(params, { arrayFormat: 'repeat' })
        },
    },
})

/**
 * Axios请求拦截器，对请求进行处理
 * 1. 序列化get请求参数
 * 2. 统一增加Authorization和TENANT-ID请求头
 * 3. 自动适配单体、微服务架构不同的URL
 * @param config AxiosRequestConfig对象，包含请求配置信息
 */

service.interceptors.request.use(
    (config) => {
        // 统一添加 token
        const userStore = useUserStore()
        if (userStore.token) {
            config.headers['Authorization'] = `Bearer ${userStore.token}`
        }

        // 处理完毕，返回config对象
        return config
    },
    (error) => {
        // 对请求错误进行处理
        return Promise.reject(error)
    },
)

service.interceptors.response.use(
    (response) => {
        console.log('请求结果', response)
        if (Object.hasOwn(response.data, 'code')) {
            const { code, msg } = response.data
            console.log('当前的code是', code)
            if (code !== 0) {
                useMessage().error(msg || '发生了错误，请稍后再试')
            }
        } else {
            console.log('该接口没有返回code')
            return response.data
        }

        return response.data
    },
    (error) => {
        console.log('发生了错误', error)
        const status = Number(error.response?.status) || 200
        const demoEnvMessages = {
            424: '用户凭证已过期',
            401: '用户凭证已过期',
            500: '系统繁忙，请联系管理员',
            503: '系统异常，请联系管理员',
        }

        if (demoEnvMessages[status]) {
            ElMessage.closeAll()
            useMessage().error(demoEnvMessages[status])

            const userStore = useUserStore()
            //  处理一些逻辑
            switch (status) {
                case 424:
                    // token过期
                    userStore.setToken('')
                    userStore.setUserInfo(null)
                    break
                case 403:
                    // 禁止访问
                    break
                case 404:
                    // 资源不存在
                    break
                case 500:
                    // 服务器错误
                    break
                default:
                    break
            }
        }

        return Promise.reject(error)
    },
)

export default service
