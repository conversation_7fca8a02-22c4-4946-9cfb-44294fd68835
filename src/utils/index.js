/**
 * 生成UUID
 * **/
export function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
        const r = (Math.random() * 16) | 0
        const v = c === 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
    })
}

/**
 * 将两个价格对比，转换为 xx折 格式（最多保留一位小数）
 * @param {number} originalPrice 原价
 * @param {number} currentPrice 现价
 * @returns {string} 返回折扣字符串，例如 "8折" 或 "8.5折"
 */
export function getPriceDiscount(originalPrice, currentPrice) {
    if (originalPrice <= 0 || currentPrice <= 0) {
        return '0折' // 避免除数为0或负数
    }

    // 计算折扣（现价 / 原价），然后乘以 10 得到 xx折 数值
    const discountRatio = currentPrice / originalPrice
    const discountValue = discountRatio * 10 // 0.8 → 8，0.85 → 8.5

    // 四舍五入保留1位小数（处理 8.49 → 8.5，8.44 → 8.4）
    const roundedDiscount = Math.round(discountValue * 10) / 10

    // 如果是整数（如 8.0 → 8折），否则保留1位小数（如 8.5折）
    const discountStr =
        roundedDiscount % 1 === 0
            ? `${roundedDiscount.toFixed(0)}折`
            : `${roundedDiscount.toFixed(1)}折`

    return discountStr
}

// 订单状态
export const orderStatus = {
    0: '待支付',
    1: '待发货',
    2: '待收货',
    3: '已完成',
    4: '已取消',
    5: '退款',
    6: '服务中',
}

// 订单状态
export const refundStatus = {
    0: '未退款',
    1: '退款中',
    2: '部分退款',
    3: '整单退款',
}

// 获取当前时间并格式化为 YYYY-MM-DD HH:MM:SS
export function getCurrentDateTime() {
    const now = new Date()

    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')

    const hours = String(now.getHours()).padStart(2, '0')
    const minutes = String(now.getMinutes()).padStart(2, '0')
    const seconds = String(now.getSeconds()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

/**
 * 获取指定日期几天后的时间
 * @param {string} dateStr - 初始日期，格式 "YYYY-MM-DD HH:MM:SS"
 * @param {number} days - 要增加的天数
 * @returns {string} 几天后的日期，格式 "YYYY-MM-DD HH:MM:SS"
 */
export function getDateAfterDays(dateStr, days) {
    // 1. 解析输入日期
    const [datePart, timePart] = dateStr.split(' ')
    const [year, month, day] = datePart.split('-').map(Number)
    const [hours, minutes, seconds] = timePart.split(':').map(Number)

    // 2. 创建 Date 对象
    const date = new Date(year, month - 1, day, hours, minutes, seconds)

    // 3. 计算几天后的时间
    date.setDate(date.getDate() + days)

    // 4. 重新格式化为 YYYY-MM-DD HH:MM:SS
    const newYear = date.getFullYear()
    const newMonth = String(date.getMonth() + 1).padStart(2, '0')
    const newDay = String(date.getDate()).padStart(2, '0')

    const newHours = String(date.getHours()).padStart(2, '0')
    const newMinutes = String(date.getMinutes()).padStart(2, '0')
    const newSeconds = String(date.getSeconds()).padStart(2, '0')

    return `${newYear}-${newMonth}-${newDay} ${newHours}:${newMinutes}:${newSeconds}`
}
