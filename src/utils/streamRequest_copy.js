/**
 * 流式请求工具函数 - 优化版
 * 用于处理 Server-Sent Events (SSE) 和流式响应
 */

import { useUserStore } from '@/stores/user.js'

/**
 * 创建流式请求
 * @param {string} url - 请求URL
 * @param {Object} data - 请求数据
 * @param {Object} options - 配置选项
 * @returns {Promise} - 返回一个可以监听事件的对象
 */
export function createStreamRequest(url, data, options = {}) {
    const {
        onMessage = () => {}, // 接收到消息时的回调
        onError = () => {},   // 发生错误时的回调
        onComplete = () => {}, // 完成时的回调
        onStart = () => {},   // 开始时的回调
        timeout = 30000,      // 超时时间(毫秒)
        method = 'POST',      // 默认POST方法
    } = options

    return new Promise((resolve, reject) => {
        const userStore = useUserStore()
        const baseURL = import.meta.env.VITE_API_URL
        const fullUrl = `${baseURL}${url}`

        // 准备请求头
        const headers = {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'tenant-id': '1',
        }

        // 添加认证头
        if (userStore.token) {
            headers['Authorization'] = `Bearer ${userStore.token}`
        }

        // 创建控制器和超时
        let eventSource = null
        let controller = new AbortController()
        let timeoutId = null
        let isCompleted = false

        // 清理函数
        const cleanup = (type = 'normal') => {
            if (isCompleted) return
            isCompleted = true

            if (timeoutId) {
                clearTimeout(timeoutId)
                timeoutId = null
            }

            if (eventSource) {
                eventSource.close()
                eventSource = null
            }

            if (controller && !controller.signal.aborted) {
                controller.abort()
                controller = null
            }

            // 如果是超时或错误导致的清理，不触发onComplete
            if (type === 'normal') {
                onComplete()
            }
        }

        // 设置超时
        if (timeout > 0) {
            timeoutId = setTimeout(() => {
                cleanup('timeout')
                const error = new Error('请求超时')
                onError(error)
                reject(error)
            }, timeout)
        }

        // 处理GET请求(使用EventSource)
        if (method === 'GET' && (!data || Object.keys(data).length === 0)) {
            try {
                eventSource = new EventSource(fullUrl)

                eventSource.onopen = () => {
                    onStart()
                }

                eventSource.onmessage = (event) => {
                    try {
                        const data = event.data ? JSON.parse(event.data) : {}
                        onMessage(data)
                    } catch (e) {
                        onMessage({ text: event.data, raw: event })
                    }
                }

                eventSource.onerror = (error) => {
                    // EventSource在错误时可能会自动重连，只有当readyState为CLOSED时才真正失败
                    if (eventSource.readyState === EventSource.CLOSED) {
                        cleanup('error')
                        onError(error)
                        reject(error)
                    }
                }

                // 监听自定义完成事件
                eventSource.addEventListener('complete', () => {
                    cleanup()
                    resolve()
                })

                return
            } catch (error) {
                console.warn('EventSource初始化失败，回退到fetch:', error)
                // 继续执行fetch逻辑
            }
        }

        // 使用fetch进行流式请求
        const fetchOptions = {
            method,
            headers,
            signal: controller.signal,
        }

        if (method !== 'GET' && data) {
            fetchOptions.body = JSON.stringify(data)
        }

        fetch(fullUrl, fetchOptions)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误! 状态码: ${response.status}`)
                }

                onStart()

                const reader = response.body.getReader()
                const decoder = new TextDecoder('utf-8')
                let buffer = ''

                const processData = (dataStr) => {
                    if (dataStr === '[DONE]') {
                        cleanup()
                        resolve()
                        return true
                    }

                    try {
                        const data = dataStr ? JSON.parse(dataStr) : null
                        if (data) onMessage(data)
                    } catch (e) {
                        if (dataStr && dataStr.trim()) {
                            onMessage({ text: dataStr })
                        }
                    }
                    return false
                }

                const readStream = () => {
                    return reader.read().then(({ done, value }) => {
                        if (done) {
                            // 处理缓冲区剩余数据
                            if (buffer) {
                                const lines = buffer.split('\n')
                                for (const line of lines.filter(l => l.trim())) {
                                    if (line.startsWith('data: ')) {
                                        if (processData(line.slice(6).trim())) return
                                    }
                                }
                            }
                            cleanup()
                            resolve()
                            return
                        }

                        buffer += decoder.decode(value, { stream: true })
                        const lines = buffer.split('\n')

                        // 保留最后不完整的行
                        buffer = lines.pop() || ''

                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                if (processData(line.slice(6).trim())) return
                            }
                        }

                        return readStream()
                    }).catch(error => {
                        cleanup('error')
                        onError(error)
                        reject(error)
                    })
                }

                return readStream()
            })
            .catch(error => {
                cleanup('error')
                const err = error.name === 'AbortError'
                    ? new Error('请求被取消')
                    : error
                onError(err)
                reject(err)
            })
    })
}

/**
 * AI 聊天流式请求
 * @param {Object} messageData - 消息数据
 * @param {Object} options - 配置选项
 * @returns {Promise}
 */
export function aiChatStream(messageData, options = {}) {
    return createStreamRequest('/ai/chat/msg', messageData, {
        ...options,
        method: 'POST'
    })
}

/**
 * GET流式请求
 * @param {string} url - 请求URL
 * @param {Object} options - 配置选项
 * @returns {Promise}
 */
export function getStreamRequest(url, options = {}) {
    return createStreamRequest(url, null, {
        ...options,
        method: 'GET'
    })
}
