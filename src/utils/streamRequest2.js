import { useUserStore } from '@/stores/user.js'

export async function fetchStream(data, onChunkReceived, onComplete, onError) {
    try {
        const baseURL = import.meta.env.VITE_API_URL
        const fullUrl = `${baseURL}/ai/chat/msg`
        const userStore = useUserStore()

        const response = await fetch(fullUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'tenant-id': 1,
                'Authorization': `Bearer ${userStore.token}`
            },
            body: JSON.stringify(data),
        })

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
        }

        const reader = response.body.getReader()
        const decoder = new TextDecoder()
        let result = ''

        while (true) {
            const { done, value } = await reader.read()
            if (done) {
                onComplete(result)
                break
            }

            const chunk = decoder.decode(value, { stream: true })
            result += chunk

            // 调用回调函数处理每个数据块
            if (onChunkReceived) {
                onChunkReceived(chunk, result)
            }
        }
    } catch (error) {
        if (onError) {
            onError(error)
        } else {
            console.error('Fetch stream error:', error)
        }
    }
}
