import { useUserStore } from '@/stores/user.js'

export async function fetchStream(data, onChunkReceived, onComplete, onError) {
    try {
        const baseURL = import.meta.env.VITE_API_URL
        const fullUrl = `${baseURL}/ai/chat/msg`
        const userStore = useUserStore()

        const response = await fetch(fullUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'tenant-id': 1,
                Authorization: `Bearer ${userStore.token}`,
            },
            body: JSON.stringify(data),
        })

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
        }

        const reader = response.body.getReader()
        const decoder = new TextDecoder()
        let buffer = ''
        let fullMessage = ''

        while (true) {
            const { done, value } = await reader.read()
            if (done) {
                if (onComplete) onComplete(fullMessage)
                break
            }

            const chunk = decoder.decode(value, { stream: true })
            buffer += chunk

            // 处理可能的多行数据
            const lines = buffer.split('\n')
            buffer = lines.pop() || ''

            for (const line of lines) {
                if (line.startsWith('data:')) {
                    const content = line.replace('data:', '').trim()
                    if (content === '[DONE]') continue

                    fullMessage += content
                    if (onChunkReceived) onChunkReceived(content, fullMessage)
                } else if (line.trim()) {
                    // 处理非标准格式的数据
                    fullMessage += line
                    if (onChunkReceived) onChunkReceived(line, fullMessage)
                }
            }
        }
    } catch (error) {
        if (onError) {
            onError(error)
        } else {
            console.error('Fetch stream error:', error)
        }
    }
}
