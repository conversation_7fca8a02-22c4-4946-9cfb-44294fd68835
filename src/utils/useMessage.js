import { ElMessage } from 'element-plus'

export function useMessage() {
    class MessageClass {
        // 普通提示
        info(title) {
            ElMessage.info(title)
        }

        // 警告提示
        warning(title) {
            ElMessage.warning(title)
        }

        // 成功提示
        success(title) {
            ElMessage.success(title)
        }

        // 错误提示
        error(title) {
            ElMessage.error(title)
        }
    }

    return new MessageClass()
}
