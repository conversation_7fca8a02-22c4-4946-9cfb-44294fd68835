// stores/otherStore.js
import { defineStore } from 'pinia'
import { getAreaCity } from '@/api/main.js'

export const useOtherStore = defineStore('other', {
    // 持久化配置
    persist: {
        storage: localStorage // 使用 sessionStorage 替代默认的 localStorage
    },

    // 状态定义
    state: () => ({
        regionList: []
    }),

    // 操作方法
    actions: {
        async loadRegion() {
            try {
                const response = await getAreaCity()
                this.regionList = response.data
            } catch (err) {
                console.error('加载区域数据失败:', err)
            }
        }
    }
})
