// src/stores/user.ts
import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
    persist: {
        storage: localStorage // 使用 sessionStorage 替代默认的 localStorage
    },
    state: () => ({
        token: '',
        userInfo: null  // 明确声明可能为 null
    }),
    getters: {
        // 判断用户是否已登录
        isLoggedIn: (state) => {
            return !!(state.token && state.userInfo)
        },
        // 获取用户名
        userName: (state) => {
            return state.userInfo?.name || state.userInfo?.username || '未知用户'
        },
        // 获取用户头像
        userAvatar: (state) => {
            return state.userInfo?.avatar || '/default-avatar.png'
        }
    },
    actions: {
        setToken(newToken) {
            this.token = newToken
        },
        clearToken() {
            this.token = ''
        },
        setUserInfo(data) {
            this.userInfo = data
        },
        clearUserInfo() {
            this.userInfo = null
        },
        // 登录方法
        login(token, userInfo) {
            this.setToken(token)
            this.setUserInfo(userInfo)
        },
        // 登出方法
        logout() {
            this.clearToken()
            this.clearUserInfo()
        }
    }
})
