// src/stores/scrollbarStore.js
import { defineStore } from 'pinia'

export const useScrollbarStore = defineStore('scrollbar', {
    state: () => ({
        isBottom: false,
        scrollTop: 0,
        scrollHeight: 0,
        clientHeight: 0,
        isScrolling: false,
        // 滚动事件监听器
        scrollListeners: new Set(),
        // 到底部事件监听器
        bottomListeners: new Set(),
    }),

    getters: {
        // 计算滚动百分比
        scrollPercentage: (state) => {
            if (state.scrollHeight <= state.clientHeight) return 0
            return (state.scrollTop / (state.scrollHeight - state.clientHeight)) * 100
        },

        // 判断是否接近底部（距离底部小于100px）
        isNearBottom: (state) => {
            return (state.scrollHeight - state.scrollTop - state.clientHeight) < 100
        }
    },

    actions: {
        // 更新滚动状态
        updateScrollState({ scrollTop, scrollHeight, clientHeight }) {
            this.scrollTop = scrollTop
            this.scrollHeight = scrollHeight
            this.clientHeight = clientHeight

            const wasBottom = this.isBottom
            this.isBottom = scrollTop + clientHeight >= scrollHeight - 5 // 5px 容差

            // 触发滚动事件
            this.notifyScrollListeners({ scrollTop, scrollHeight, clientHeight })

            // 如果刚到达底部，触发底部事件
            if (!wasBottom && this.isBottom) {
                this.notifyBottomListeners()
            }
        },

        // 添加滚动监听器
        addScrollListener(callback) {
            this.scrollListeners.add(callback)
            return () => this.scrollListeners.delete(callback)
        },

        // 添加到底部监听器
        addBottomListener(callback) {
            this.bottomListeners.add(callback)
            return () => this.bottomListeners.delete(callback)
        },

        // 通知滚动监听器
        notifyScrollListeners(scrollData) {
            this.scrollListeners.forEach(callback => {
                try {
                    callback(scrollData)
                } catch (error) {
                    console.error('Scroll listener error:', error)
                }
            })
        },

        // 通知底部监听器
        notifyBottomListeners() {
            this.bottomListeners.forEach(callback => {
                try {
                    callback()
                } catch (error) {
                    console.error('Bottom listener error:', error)
                }
            })
        },

        // 设置滚动状态
        setScrolling(isScrolling) {
            this.isScrolling = isScrolling
        },

        // 兼容旧方法
        updateScroll(bool) {
            this.isBottom = bool
        },
    },
})
