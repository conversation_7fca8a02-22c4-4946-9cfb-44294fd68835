// stores/cartStore.js
import { defineStore } from 'pinia'
import { addCart, addCartNum, cartCount, cartList, clearCart, deleteCart } from '@/api/shop-cart.js'
import { useMessage } from '@/utils/useMessage.js'

export const useCartStore = defineStore('cart', {
    // 持久化配置
    persist: {
        storage: localStorage, // 使用 sessionStorage 替代默认的 localStorage
    },

    // 状态定义
    state: () => ({
        items: [],
        isLoading: false,
        error: null,
        totalCart: 0, // 购物车商品数量
    }),

    // 计算属性
    getters: {
        totalItems() {
            return this.items.reduce((total, item) => total + item.quantity, 0)
        },
        totalPrice() {
            return this.items.reduce((total, item) => total + item.price * item.quantity, 0)
        },
        itemCount: (state) => (productId) => {
            const item = state.items.find((item) => item.id === productId)
            return item ? item.quantity : 0
        },
    },

    // 操作方法
    actions: {
        // 添加商品到购物车
        async addToCart(params) {
            try {
                const response = await addCart(params)
                console.log('商品添加到购物车结果', response)
                if (response && response.code === 0) {
                    useMessage().success('添加到购物车成功')
                    // 添加成功则重新加载购物车列表
                    await this.loadCart()
                }
            } catch (err) {
                console.error('商品添加失败:', err)
            }
        },

        // 从购物车移除商品
        async removeFromCart(productIds) {
            try {
                let params = {
                    ids: productIds,
                }

                const response = await deleteCart(params)
                console.log('商品删除结果', response)
                if (response && response.code === 0) {
                    useMessage().success('删除成功')
                    // 添加成功则重新加载购物车列表
                    await this.loadCart()
                }
            } catch (err) {
                console.error('商品删除失败:', err)
            }
        },

        // 更新商品数量
        async updateQuantity(params) {
            try {
                const response = await addCartNum(params)
                console.log('更新商品的数量结果', response)
                if (response && response.code === 0) {
                    // 添加成功则重新加载购物车列表
                    // await this.loadCart()
                }
            } catch (err) {
                console.error('更新商品的数量失败:', err)
            }
        },

        // 清空购物车
        async clearCart() {
            await clearCart()
            await this.loadCart()
        },

        // 加载购物车数据
        async loadCart() {
            try {
                this.error = null
                // 这里替换为实际的API调用
                const response = await cartList()
                await this.getCount()
                this.items = this.groupByTenantId(response.data)
                console.log('购物车列表', this.items)
                this.totalCart = response.data.length || 0
            } catch (err) {
                this.error = '加载购物车失败'
                console.error('加载购物车失败:', err)
            } finally {
                this.isLoading = false
            }
        },
        async getCount() {
            // 订单数量汇总
            try {
                this.error = null
                // 这里替换为实际的API调用
                const response = await cartCount()
                console.log('购物车商品数量汇总', response)
                this.totalCart = response.data
            } catch (err) {
                console.error('购物车商品数量汇总失败:', err)
            } finally {
                this.isLoading = false
            }
        },
        groupByTenantId(data) {
            // 根据企业的租户ID分组
            const result = []
            const companyMap = new Map() // 用于存储已处理的公司信息

            data.forEach((item) => {
                const tenantId = item.companyInfoVo?.tenantId
                if (!tenantId) return

                if (!companyMap.has(tenantId)) {
                    // 创建新的公司节点
                    const companyNode = {
                        id: item.companyInfoVo?.id,
                        name: item.companyInfoVo?.name,
                        tenantId: item.companyInfoVo?.tenantId,
                        logo: item.companyInfoVo?.logo,
                        legalPerson: item.companyInfoVo?.legalPerson,
                        children: [
                            {
                                id: item.id,
                                userId: item.userId,
                                shopId: item.shopId,
                                goodsId: item.goodsId,
                                goodsName: item.goodsName,
                                goodsMainImg: item.goodsMainImg,
                                buyPrice: item.buyPrice,
                                num: item.num,
                                createTime: item.createTime,
                                updateTime: item.updateTime,
                                goodsStatus: item.goodsStatus,
                                cateName: item.cateName,
                                oriPrice: item.oriPrice,
                                selected: false,
                            },
                        ],
                    }
                    companyMap.set(tenantId, companyNode)
                    result.push(companyNode)
                } else {
                    // 添加到已有公司的children中
                    companyMap.get(tenantId).children.push({
                        id: item.id,
                        userId: item.userId,
                        shopId: item.shopId,
                        goodsId: item.goodsId,
                        goodsName: item.goodsName,
                        goodsMainImg: item.goodsMainImg,
                        buyPrice: item.buyPrice,
                        num: item.num,
                        createTime: item.createTime,
                        updateTime: item.updateTime,
                        goodsStatus: item.goodsStatus,
                        cateName: item.cateName,
                        oriPrice: item.oriPrice,
                        selected: false,
                    })
                }
            })

            return result
        },
    },
})
