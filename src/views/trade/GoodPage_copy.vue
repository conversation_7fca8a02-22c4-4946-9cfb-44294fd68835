<template>
    <div class="goods-page">
        <!-- 搜索筛选区域 -->
        <div class="search-filter-section">
            <AiSearch :show-car="true" @search="handleAISearch" />
        </div>

        <!--  分类区域  -->
        <div class="filter-box flex-row jc-space-between ai-center">
            <category :list="cateList" @cate="handleCate"></category>
        </div>

        <!-- 商品列表 -->
        <div v-loading="loading" class="goods-list-section">
            <!-- 网格视图 -->
            <div class="goods-grid">
                <good-item
                    v-for="item in goodsList"
                    :key="item.id"
                    :item="item"
                    @item-click="handleGoodsClick"
                >
                </good-item>
            </div>

            <!-- 空状态 -->
            <el-empty
                v-if="!loading && goodsList.length === 0"
                description="暂无商品数据"
            ></el-empty>

            <!-- 加载更多提示 -->
            <load-more-tip
                :data-length="goodsList.length"
                :loading="isLoadingMore"
                :has-more="hasMoreData"
            />
        </div>
    </div>

    <!--  商品详情的弹窗  -->
    <good-detail-modal
        ref="goodModalRef"
        :product="selectedProduct"
        @join-group="handleJoinGroup"
    ></good-detail-modal>
</template>

<script setup>
import { getGoodDetail, getGoodsPage, goodsCateList } from '@/api/goods.js'
import { useMessage } from '@/utils/useMessage'
import { useInfiniteScroll } from '@/composables/useInfiniteScroll.js'
import { aiSearch } from '@/api/main.js'

import GoodItem from '@/components/GoodItem/GoodItem.vue'
import AiSearch from '@/components/AiSearch/AiSearch.vue'
import GoodDetailModal from '@/views/trade/components/GoodDetailModal.vue'
import LoadMoreTip from '@/components/LoadMoreTip/LoadMoreTip.vue'
import Category from '@/components/Category/Category.vue'

// 响应式数据
const loading = ref(false)
const goodsList = ref([])
const total = ref(0)
const cateList = ref([
    {
        id: null,
        icon: 'quanbu',
        name: '全部',
    },
])
const goodModalRef = ref()

// 搜索表单
const searchForm = reactive({
    title: '', // 商品名称
    catePid: '', // 分类的一级ID
})

// 分页参数
const pagination = reactive({
    current: 1,
    size: 20, // 改为每页20条，更适合下拉加载
})

// 当前查询是不是AI 查询
const isAIQuery = ref(false)
const searchName = ref('')

// 下拉加载相关状态
const hasMoreData = ref(true)
const isLoadingMore = ref(false) // 防止重复加载的标志

// 弹窗的一些设置
const selectedProduct = ref({})

// 获取商品列表
const fetchGoodsList = async (isLoadMore = false) => {
    try {
        loading.value = true

        // 构建查询参数
        const params = {
            current: pagination.current,
            size: pagination.size,
            ...searchForm,
        }

        console.log('请求参数:', params)

        // 尝试调用真实接口，如果失败则使用模拟数据
        let response
        try {
            response = await getGoodsPage(params)
        } catch (error) {
            useMessage().error(error.msg || '获取商品列表失败')
            // 确保在错误时也重置加载状态
            loading.value = false
            return
        }

        if (response.code === 0) {
            const newData = response.data.records || []

            if (isLoadMore) {
                // 下拉加载：追加数据
                goodsList.value = [...goodsList.value, ...newData]
            } else {
                // 首次加载或搜索：替换数据
                goodsList.value = newData
            }

            total.value = response.data.total || 0

            // 只在首次加载时更新分页信息
            if (!isLoadMore) {
                pagination.current = response.data.current || 1
                pagination.size = response.data.size || 20
            }

            // 检查是否还有更多数据
            hasMoreData.value = goodsList.value.length < total.value
        } else {
            useMessage().error(response.msg || '获取商品列表失败')
            if (!isLoadMore) {
                goodsList.value = []
                total.value = 0
            }
        }
    } catch (error) {
        console.error('获取商品列表失败:', error)
        useMessage().error('获取商品列表失败')
        if (!isLoadMore) {
            goodsList.value = []
            total.value = 0
        }
    } finally {
        loading.value = false
    }
}

// 商品点击处理
const handleGoodsClick = (item) => {
    console.log('点击商品:', item)
    // 一定要用showModal，不然图片会显示异常，不会测试的话，永远不会复现，嘿嘿
    nextTick(async () => {
        const { data } = await getGoodDetail({ id: item.id })
        console.log('data', data)
        // 丰富商品数据
        selectedProduct.value = {
            ...item,
            ...data,
        }
        goodModalRef.value.openModal(selectedProduct)
    })
}

// 参团处理
const handleJoinGroup = async (groupData) => {
    console.log('参团:', groupData)
    // 先判断是否可以参团
    try {
        // const response = await isJoinGroup({
        //     groupId: '',
        // })
        // if (response.code === 0) {
        //     // 可以参团，直接到确认订单页面
        //     let params = {
        //         userAddressId: '',
        //         goodsList: [
        //             {
        //                 goodsId: groupData.productId,
        //                 num: 1,
        //                 shopId: selectedProduct.value.companyInfoVo.id,
        //             },
        //         ],
        //     }
        //     goodModalRef.value.closeModal()
        //     orderPlaceRef.value.openDrawer(params)
        // } else {
        //     useMessage().error(response.msg || '参团失败')
        // }
    } catch (error) {
        useMessage().error(error.msg || '参团失败')
    }
}

// 搜索处理
const handleSearch = () => {
    // 重置分页和状态
    pagination.current = 1
    hasMoreData.value = true
    // 重新获取数据（替换而不是追加）
    fetchGoodsList(false)
}

// 重置数据
const resetData = () => {
    pagination.current = 1
    goodsList.value = []
    total.value = 0
    hasMoreData.value = true
}

// 加载更多数据的函数
const loadMoreData = async () => {
    // 防止重复加载
    if (!hasMoreData.value || isLoadingMore.value || loading.value) {
        return
    }

    try {
        isLoadingMore.value = true
        pagination.current += 1
        await fetchGoodsList(true)
    } finally {
        isLoadingMore.value = false
    }
}

// 设置无限滚动
const { loading: infiniteScrollLoading } = useInfiniteScroll({
    loadMore: loadMoreData,
    hasMore: hasMoreData,
    threshold: 100,
})

// 加载商品分类，只显示一级
const loadGoodsCate = async () => {
    try {
        const response = await goodsCateList({ pid: 0 })
        cateList.value = [...cateList.value, ...response.data]
        console.log('商品分类', cateList.value)
    } catch (error) {
        console.error('获取商品分类失败:', error)
    }
}

// 分类搜索
const handleCate = (item) => {
    searchForm.catePid = item ? item.id : ''
    handleSearch()
}

const handleAISearch = async (keyWord) => {
    console.log('搜索关键词', keyWord)

    // 如果有 keyWord  ，则是AI查询，否则是普通查询
    isAIQuery.value = !!keyWord
    searchName.value = keyWord
    pagination.current = 1

    handleSearch()
}

const loadAIData = async () => {
    try {
        let searchParams = {
            type: 1, // 查询类型 1.产品 2.服务 3.供需 4.企业
            query: searchName.value, // 	查询内容
            current: pagination.current,
            size: pagination.size,
        }

        const response = await aiSearch(searchParams)
        console.log('搜索结果', response)
    } catch (error) {
        console.error('搜索失败:', error)
    } finally {
        console.log('AI搜索完成')
    }
}

// 组件挂载时获取数据
onMounted(() => {
    loadGoodsCate()
    fetchGoodsList()
})

// keep-alive 组件激活时的处理
onActivated(() => {
    // 可以在这里添加数据刷新逻辑，如果需要的话
    // 例如：检查数据是否过期，是否需要重新获取
})

// keep-alive 组件失活时的处理
onDeactivated(() => {
    // 重置加载状态，防止下次激活时状态异常
    isLoadingMore.value = false
})
</script>

<style scoped lang="scss">
.goods-page {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: calc(100vh - 60px);

    // 搜索筛选区域
    .search-filter-section {
        margin-bottom: 20px;
    }

    .filter-box {
        margin-bottom: 15px;
    }

    // 商品列表区域
    .goods-list-section {
        margin-bottom: 20px;

        // 网格视图
        .goods-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
        }
    }

    // 响应式设计
    @media (max-width: 768px) {
        padding: 10px;

        .toolbar-section {
            flex-direction: column;
            gap: 16px;
            align-items: stretch;

            .sort-options {
                justify-content: center;
            }

            .view-toggle {
                align-self: center;
            }
        }

        .goods-grid {
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 16px;
            padding: 16px;
        }
    }
}
</style>
