<template>
    <el-scrollbar class="hide-scrollbar">
        <div class="product-info">
            <!-- 商品图片轮播 -->
            <div class="product-gallery flex-row">
                <div class="left-top-left">
                    <el-scrollbar ref="scrollbarRef" height="403px">
                        <div ref="thumbnailsContainer" class="flex-column">
                            <template v-for="(item, index) in imgList" :key="index">
                                <div
                                    ref="thumbnails"
                                    class="thumbnail-box"
                                    :class="{ _active: activeIndex === index }"
                                    @click="thumbHover(index)"
                                >
                                    <el-image class="thumbnail-img" :src="item"></el-image>
                                </div>
                            </template>
                        </div>
                    </el-scrollbar>
                </div>
                <div class="left-top-right">
                    <el-carousel
                        ref="carouselRef"
                        indicator-position="none"
                        :autoplay="false"
                        :initial-index="activeIndex"
                        height="403px"
                        @change="changeBanner"
                    >
                        <template v-for="(item, index) in imgList" :key="index">
                            <el-carousel-item>
                                <div class="flex-row jc-center">
                                    <img class="main-img" :src="item" />
                                </div>
                            </el-carousel-item>
                        </template>
                    </el-carousel>
                </div>
            </div>

            <!-- 商品基本信息 -->
            <div class="product-basic-info">
                <div class="product-title text-over2">{{ product.title }}</div>
                <p class="product-subtitle">{{ product.subtitle }}</p>

                <!-- 价格信息 -->
                <div class="price-section">
                    <div class="current-price">
                        <span class="currency">¥</span>
                        <span class="amount">{{ product.groupPrice || product.price }}</span>
                        <span v-if="product.groupPrice" class="price-label">拼团价</span>
                    </div>
                    <div
                        v-if="product.originalPrice && product.originalPrice > product.price"
                        class="original-price"
                    >
                        <span class="currency">¥</span>
                        <span class="amount">{{ product.originalPrice }}</span>
                    </div>
                </div>

                <!-- 拼团进度 -->
                <div v-if="product.type === 3" class="group-progress">
                    <div class="progress-header">
                        <h3>拼团进度</h3>
                        <span class="time-left">{{ remainingTime }} </span>
                    </div>

                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 90%"></div>
                    </div>

                    <div class="progress-info">
                        <span>已拼 {{ product.groupCurrNum }} 人</span>
                        <span>还需 {{ product.groupNum - product.groupCurrNum }} 人</span>
                    </div>

                    <!-- 参团用户头像 -->
                    <div
                        v-if="product['groupUserAllList'] && product['groupUserAllList'].length > 0"
                        class="participants"
                    >
                        <div class="user-list">
                            <template v-for="user in product['groupUserAllList']" :key="user.id">
                                <el-image :src="user.avatar" class="avatar"></el-image>
                            </template>
                        </div>
                    </div>
                </div>

                <!--  协会信息  -->
                <div v-if="product.associationInfoVo" class="association-info flex-row ai-center">
                    <div class="_left flex-row jc-center ai-center">
                        <el-image :src="product.associationInfoVo.logo"></el-image>
                    </div>
                    <div class="_right">
                        {{ product.associationInfoVo?.name }}
                    </div>
                </div>

                <!--  企业信息  -->
                <div v-if="product.companyInfoVo" class="company-info flex-row ai-center">
                    <div class="_left flex-row jc-center ai-center">
                        <el-image :src="product.companyInfoVo.logo"></el-image>
                    </div>
                    <div class="_right">
                        {{ product.companyInfoVo?.name }}
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <template v-if="product.type === 3">
                        <button class="add-cart-btn" @click="handleShare">立即分享</button>
                        <button class="join-group-btn" @click="handleBuyNow">立即参团</button>
                    </template>
                    <template v-else>
                        <button class="add-cart-btn" @click="handleAddToCart">加入购物车</button>
                        <button class="buy-now-btn" @click="handleBuyNow">立即购买</button>
                    </template>
                </div>

                <!--  详情信息  -->
                <div class="detail-info">
                    <div class="_title">商品描述</div>
                    <div class="detail-content" v-html="product.detail"></div>
                </div>
            </div>
        </div>
    </el-scrollbar>
</template>

<script setup>
import { useCartStore } from '@/stores/shop-cart.js'
import { useAuth } from '@/composables/useAuth.js'

const cart = useCartStore()

const props = defineProps({
    product: {
        type: Object,
        required: true,
    },
})

// Emits
const emit = defineEmits(['join-group', 'buy-now'])

const imgList = ref([])
const activeIndex = ref(0)
const carouselRef = ref(null)
const scrollbarRef = ref(null)
const thumbnails = ref([]) // 存储所有缩略图 DOM 元素
const remainingTime = ref('')

watch(
    () => props.product, // 监听 props
    (newVal) => {
        imgList.value.push(newVal['mainImg'])

        if (newVal['goodsImg']) {
            imgList.value = [...imgList.value, ...newVal['goodsImg'].split(',')]
        }
    },
    { immediate: true }, // 组件创建时立即执行一次
)

/**
 * 轮播图切换时滚动到对应的缩略图
 */
const changeBanner = (index) => {
    activeIndex.value = index
    scrollToThumbnail(index)
}

/**
 * 鼠标悬停缩略图时切换轮播图
 */
const thumbHover = (index) => {
    activeIndex.value = index
    carouselRef.value?.setActiveItem(index)
    scrollToThumbnail(index)
}

/**
 * 滚动 el-scrollbar 到指定的缩略图位置
 */
const scrollToThumbnail = (index) => {
    nextTick(() => {
        if (thumbnails.value && thumbnails.value[index]) {
            const thumbnail = thumbnails.value[index]
            const scrollTop = thumbnail.offsetTop // 直接使用 offsetTop 计算位置
            scrollbarRef.value?.setScrollTop(scrollTop)
            // scrollbarRef.value?.scrollTo({ top: scrollTop, behavior: 'smooth' });
        }
    })
}

// 计算剩余时间
const createCountdown = (targetDate, callback) => {
    let rafId = null
    let lastUpdateTime = 0
    const UPDATE_INTERVAL = 1000 // 每秒更新一次显示（可调整）

    // 核心计算逻辑
    function getRemainingTime() {
        const now = new Date()
        const diff = targetDate - now

        if (diff <= 0) {
            return { days: 0, hours: 0, minutes: 0, seconds: 0, milliseconds: 0 }
        }

        return {
            days: Math.floor(diff / (1000 * 60 * 60 * 24)),
            hours: Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
            minutes: Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60)),
            seconds: Math.floor((diff % (1000 * 60)) / 1000),
            milliseconds: diff % 1000,
        }
    }

    // 动画帧循环
    function update(currentTime) {
        if (!lastUpdateTime) lastUpdateTime = currentTime
        const elapsed = currentTime - lastUpdateTime

        // 控制更新频率（避免每帧都计算）
        if (elapsed >= UPDATE_INTERVAL) {
            callback(getRemainingTime())
            lastUpdateTime = currentTime
        }

        rafId = requestAnimationFrame(update)
    }

    // 启动倒计时
    function start() {
        rafId = requestAnimationFrame(update)
        return stop
    }

    // 停止倒计时
    function stop() {
        if (rafId) {
            cancelAnimationFrame(rafId)
        }
    }

    return { start, stop }
}

const handleBuyNow = () => {
    emit('buy-now', props.product)
}

const handleShare = () => {}
const { executeWithAuthAsync } = useAuth()
// 添加到购物车
const handleAddToCart = () => {
    // 构建查询参数
    const params = {
        goodsId: props.product?.id,
        num: 1,
    }
    executeWithAuthAsync(async () => {
        await cart.addToCart(params)
    }, '')
}

onMounted(() => {
    const targetDate = new Date(props.product.groupLimitHours)
    const countdown = createCountdown(targetDate, ({ days, hours, minutes, seconds }) => {
        remainingTime.value = `剩余 ${days}天${hours}时${minutes}分${seconds}秒`
    })

    // 启动倒计时
    countdown.start()
})
</script>

<style lang="scss" scoped>
.product-info {
    padding: 24px;

    .product-gallery {
        width: 100%;
        gap: 16px;
        margin-bottom: 24px;

        .left-top-left {
            width: 130px;

            .flex-column {
                gap: 5px;

                .thumbnail-box {
                    width: 120px;
                    height: 120px;
                    border-radius: 5px;
                    overflow: hidden;
                    border: 1px solid #ffffff;
                    padding: 1px;

                    &:hover,
                    &._active {
                        border-color: #1a73e8;
                        cursor: pointer;
                    }

                    .thumbnail-img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        border-radius: 5px;
                    }
                }
            }
        }

        .left-top-right {
            width: calc(100% - 148px);

            .main-img {
                max-width: 100%;
                max-height: 403px;
                object-fit: cover;
                border-radius: 10px;
            }
        }
    }

    .product-basic-info {
        margin-bottom: 32px;

        .product-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .product-subtitle {
            font-size: 16px;
            color: #666;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .price-section {
            display: flex;
            align-items: baseline;
            gap: 16px;
            margin-bottom: 24px;

            .current-price {
                display: flex;
                align-items: baseline;
                gap: 4px;

                .currency {
                    font-size: 18px;
                    color: #ff4757;
                    font-weight: 600;
                }

                .amount {
                    font-size: 32px;
                    color: #ff4757;
                    font-weight: 700;
                }

                .price-label {
                    font-size: 14px;
                    color: #ff4757;
                    background: #fff5f5;
                    padding: 2px 8px;
                    border-radius: 4px;
                    margin-left: 8px;
                }
            }

            .original-price {
                display: flex;
                align-items: baseline;
                gap: 2px;
                text-decoration: line-through;
                color: #999;

                .currency {
                    font-size: 14px;
                }

                .amount {
                    font-size: 16px;
                }
            }
        }
    }

    .group-progress {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 12px;
        margin-bottom: 24px;

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;

            h3 {
                font-size: 16px;
                font-weight: 600;
                color: #333;
            }

            .time-left {
                font-size: 14px;
                color: #ff4757;
                font-weight: 500;
            }
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 12px;

            .progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #ff6b6b, #ff8e8e);
                transition: width 0.3s ease;
            }
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            color: #666;
            margin-bottom: 16px;
        }

        .participants {
            .user-list {
                display: flex;
                align-items: center;
                gap: -8px;

                .avatar {
                    width: 32px;
                    height: 32px;
                    border-radius: 50%;
                    border: 2px solid white;
                    margin-left: -8px;

                    &:first-child {
                        margin-left: 0;
                    }
                }
            }
        }
    }

    .association-info {
        background: #222222;
        padding: 10px;
        border-radius: 12px;
        margin-bottom: 24px;
        gap: 10px;

        ._left {
            .el-image {
                width: 40px;
                height: 40px;
                object-fit: cover;
                border-radius: 5px;
            }
        }

        ._right {
            font-size: 14px;
            font-weight: 550;
            color: #fff;
        }
    }

    .company-info {
        background: #e9ecef;
        padding: 10px;
        border-radius: 12px;
        margin-bottom: 24px;
        gap: 10px;

        ._left {
            .el-image {
                width: 40px;
                height: 40px;
                object-fit: cover;
                border-radius: 5px;
            }
        }

        ._right {
            font-size: 14px;
            font-weight: 550;
            color: #333;
        }
    }

    .action-buttons {
        display: flex;
        gap: 12px;
        margin-bottom: 32px;

        button {
            flex: 1;
            height: 48px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;

            &.join-group-btn {
                background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
                color: white;

                &:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
                }
            }

            &.buy-now-btn {
                background: #409eff;
                color: white;

                &:hover {
                    background: #337ecc;
                }
            }

            &.add-cart-btn {
                background: white;
                color: #409eff;
                border: 1px solid #409eff;

                &:hover {
                    background: #f0f9ff;
                }
            }
        }
    }

    .detail-info {
        width: 100%;
        background: #f8f9fa;
        padding: 20px;
        border-radius: 12px;
        margin-bottom: 24px;

        ._title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .product-info {
        padding: 16px;

        .product-basic-info {
            .product-title {
                font-size: 18px;
            }

            .price-section {
                .current-price {
                    .amount {
                        font-size: 28px;
                    }
                }
            }
        }

        .action-buttons {
            flex-direction: column;

            button {
                height: 44px;
            }
        }
    }
}
</style>
