<template>
    <el-dialog
        v-model="dialogVisible"
        class="good-dialog"
        width="1000px"
        :destroy-on-close="true"
        :before-close="closeModal"
        top="5vh"
    >
        <!-- 主要内容区域 -->
        <div class="modal-content">
            <!-- 左侧：商品详情 -->
            <div class="product-section">
                <supply-info :supplys="supplys" @buy-now="handleBuyNow"></supply-info>
            </div>

            <!-- 右侧：在线聊天 -->
            <div class="chat-section">
                <chat-panel :supplys="supplys"></chat-panel>
            </div>
        </div>
    </el-dialog>
    <!-- 立即购买 -->
    <supply-place-pop ref="orderPlaceRef"></supply-place-pop>
</template>

<script setup>
import ChatPanel from '@/components/ChatPanel/ChatPanel.vue'
import SupplyInfo from '@/views/trade/components/SupplyInfo.vue'
import SupplyPlacePop from '@/components/OrderPlacePop/SupplyPlacePop.vue'

// Props
const props = defineProps({
    supplys: {
        type: Object,
        default: () => ({}),
    },
})

const dialogVisible = ref(false)

// Emits
const emit = defineEmits(['close', 'join-group'])

// 确认订单
const orderPlaceRef = ref(null)

const openModal = () => {
    dialogVisible.value = true
}

// 关闭弹窗
const closeModal = () => {
    dialogVisible.value = false
    emit('close')
}

// 立即购买
const handleBuyNow = () => {
    closeModal()
    orderPlaceRef.value.openDrawer(props.supplys)
}

defineExpose({
    openModal,
})
</script>

<style lang="scss">
.good-dialog {
    padding: 10px 0 !important;

    .modal-content {
        width: 100%;
        display: flex;
        height: 80vh;
        min-height: 600px;

        .product-section {
            flex: 1;
            background: #fff;
            border-right: 1px solid #e8eaed;
        }

        .chat-section {
            width: 400px;
            background: #f8f9fa;
            display: flex;
            flex-direction: column;
        }
    }

    // 响应式设计
    @media (max-width: 1024px) {
        .modal-content {
            height: 85vh;
            min-height: 500px;

            .chat-section {
                width: 350px;
            }
        }
    }

    @media (max-width: 768px) {
        .modal-content {
            flex-direction: column;
            height: 100vh;

            .product-section {
                flex: 1;
                border-right: none;
                border-bottom: 1px solid #e8eaed;
            }

            .chat-section {
                width: 100%;
                height: 300px;
                flex-shrink: 0;
            }
        }
    }

    @media (max-width: 480px) {
        .modal-content {
            .chat-section {
                height: 250px;
            }
        }
    }
}
</style>
