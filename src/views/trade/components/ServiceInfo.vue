<template>
    <el-scrollbar class="hide-scrollbar">
        <div class="product-info">
            <!-- 商品图片轮播 -->
            <div class="product-gallery flex-row">
                <div class="left-top-left">
                    <el-scrollbar ref="scrollbarRef" height="403px">
                        <div ref="thumbnailsContainer" class="flex-column">
                            <template v-for="(item, index) in imgList" :key="index">
                                <div
                                    ref="thumbnails"
                                    class="thumbnail-box"
                                    :class="{ _active: activeIndex === index }"
                                    @click="thumbHover(index)"
                                >
                                    <el-image class="thumbnail-img" :src="item"></el-image>
                                </div>
                            </template>
                        </div>
                    </el-scrollbar>
                </div>
                <div class="left-top-right">
                    <el-carousel
                        ref="carouselRef"
                        indicator-position="none"
                        :autoplay="false"
                        :initial-index="activeIndex"
                        height="403px"
                        @change="changeBanner"
                    >
                        <template v-for="(item, index) in imgList" :key="index">
                            <el-carousel-item>
                                <div class="flex-row jc-center">
                                    <img class="main-img" :src="item" />
                                </div>
                            </el-carousel-item>
                        </template>
                    </el-carousel>
                </div>
            </div>

            <!-- 商品基本信息 -->
            <div class="product-basic-info">
                <div class="product-title text-over2">{{ services.title }}</div>
                <p class="product-subtitle">{{ services.subtitle }}</p>

                <!-- 价格信息 -->
                <div class="price-section">
                    <div class="current-price">
                        <span v-if="services.saleType === 1" class="currency">¥</span>
                        <span class="amount">{{ services.showPrice }}</span>
                    </div>
                </div>

                <!--  协会信息  -->
                <div v-if="services.associationInfoVo" class="association-info flex-row ai-center">
                    <div class="_left flex-row jc-center ai-center">
                        <el-image :src="services.associationInfoVo.logo"></el-image>
                    </div>
                    <div class="_right">
                        {{ services.associationInfoVo?.name }}
                    </div>
                </div>

                <!--  企业信息  -->
                <div v-if="services.companyInfo" class="company-info flex-row ai-center">
                    <div class="_left flex-row jc-center ai-center">
                        <el-image :src="services.companyInfo.logo"></el-image>
                    </div>
                    <div class="_right">
                        {{ services.companyInfo?.name }}
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <button class="add-cart-btn" @click="handleShare">分享</button>
                    <button
                        v-if="services.saleType === 1"
                        class="buy-now-btn"
                        @click="handleBuyNow"
                    >
                        立即购买
                    </button>
                    <el-popover v-else title="联系信息" trigger="click" placement="top-start">
                        <template #reference>
                            <button class="buy-now-btn" @click="handleDiscuss">联系服务商</button>
                        </template>

                        <div class="link-info flex-column">
                            <div class="_label">{{ services.contacts || '联系人' }}</div>
                            <div class="_value">{{ services.contactInfo || '暂无联系方式' }}</div>
                        </div>
                    </el-popover>
                </div>

                <!--  详情信息  -->
                <div class="detail-info">
                    <div class="_title">服务描述</div>
                    <div class="detail-content" v-html="services.details"></div>
                </div>
            </div>
        </div>
    </el-scrollbar>
</template>

<script setup>
import { useMessage } from '@/utils/useMessage.js'

const props = defineProps({
    services: {
        type: Object,
        required: true,
    },
})

// Emits
const emit = defineEmits(['discuss', 'buy-now'])

const imgList = ref([])
const activeIndex = ref(0)
const carouselRef = ref(null)
const scrollbarRef = ref(null)
const thumbnails = ref([]) // 存储所有缩略图 DOM 元素

watch(
    () => props.services, // 监听 props
    (newVal) => {
        imgList.value.push(newVal['mainImg'])

        if (newVal['carouselImg']) {
            imgList.value = [...imgList.value, ...newVal['carouselImg'].split(',')]
        }
    },
    { immediate: true }, // 组件创建时立即执行一次
)

/**
 * 轮播图切换时滚动到对应的缩略图
 */
const changeBanner = (index) => {
    activeIndex.value = index
    scrollToThumbnail(index)
}

/**
 * 鼠标悬停缩略图时切换轮播图
 */
const thumbHover = (index) => {
    activeIndex.value = index
    carouselRef.value?.setActiveItem(index)
    scrollToThumbnail(index)
}

/**
 * 滚动 el-scrollbar 到指定的缩略图位置
 */
const scrollToThumbnail = (index) => {
    nextTick(() => {
        if (thumbnails.value && thumbnails.value[index]) {
            const thumbnail = thumbnails.value[index]
            const scrollTop = thumbnail.offsetTop // 直接使用 offsetTop 计算位置
            scrollbarRef.value?.setScrollTop(scrollTop)
        }
    })
}

const handleBuyNow = () => {
    emit('buy-now', props.services)
}

const handleDiscuss = () => {
    emit('discuss', props.supplys)
}

// 添加到购物车
const handleShare = async () => {
    useMessage().success('等待完善')
}

onMounted(() => {})
</script>

<style lang="scss" scoped>
.product-info {
    padding: 24px;

    .product-gallery {
        width: 100%;
        gap: 16px;
        margin-bottom: 24px;

        .left-top-left {
            width: 130px;

            .flex-column {
                gap: 5px;

                .thumbnail-box {
                    width: 120px;
                    height: 120px;
                    border-radius: 5px;
                    overflow: hidden;
                    border: 1px solid #ffffff;
                    padding: 1px;

                    &:hover,
                    &._active {
                        border-color: #1a73e8;
                        cursor: pointer;
                    }

                    .thumbnail-img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        border-radius: 5px;
                    }
                }
            }
        }

        .left-top-right {
            width: calc(100% - 148px);

            .main-img {
                max-width: 100%;
                max-height: 403px;
                object-fit: cover;
                border-radius: 10px;
            }
        }
    }

    .product-basic-info {
        margin-bottom: 32px;

        .product-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .product-subtitle {
            font-size: 16px;
            color: #666;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .price-section {
            display: flex;
            align-items: baseline;
            gap: 16px;
            margin-bottom: 24px;

            .current-price {
                display: flex;
                align-items: baseline;
                gap: 4px;

                .currency {
                    font-size: 18px;
                    color: #ff4757;
                    font-weight: 600;
                }

                .amount {
                    font-size: 32px;
                    color: #ff4757;
                    font-weight: 700;
                }

                .price-label {
                    font-size: 14px;
                    color: #ff4757;
                    background: #fff5f5;
                    padding: 2px 8px;
                    border-radius: 4px;
                    margin-left: 8px;
                }
            }

            .original-price {
                display: flex;
                align-items: baseline;
                gap: 2px;
                text-decoration: line-through;
                color: #999;

                .currency {
                    font-size: 14px;
                }

                .amount {
                    font-size: 16px;
                }
            }
        }
    }

    .association-info {
        background: #222222;
        padding: 10px;
        border-radius: 12px;
        margin-bottom: 24px;
        gap: 10px;

        ._left {
            .el-image {
                width: 40px;
                height: 40px;
                object-fit: cover;
                border-radius: 5px;
            }
        }

        ._right {
            font-size: 14px;
            font-weight: 550;
            color: #fff;
        }
    }

    .company-info {
        background: #e9ecef;
        padding: 10px;
        border-radius: 12px;
        margin-bottom: 24px;
        gap: 10px;

        ._left {
            .el-image {
                width: 40px;
                height: 40px;
                object-fit: cover;
                border-radius: 5px;
            }
        }

        ._right {
            font-size: 14px;
            font-weight: 550;
            color: #333;
        }
    }

    .action-buttons {
        display: flex;
        gap: 12px;
        margin-bottom: 32px;

        button {
            flex: 1;
            height: 48px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;

            &.buy-now-btn {
                background: #409eff;
                color: white;

                &:hover {
                    background: #337ecc;
                }
            }

            &.add-cart-btn {
                background: white;
                color: #409eff;
                border: 1px solid #409eff;

                &:hover {
                    background: #f0f9ff;
                }
            }
        }
    }

    .detail-info {
        width: 100%;
        background: #f8f9fa;
        padding: 20px;
        border-radius: 12px;
        margin-bottom: 24px;

        ._title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .product-info {
        padding: 16px;

        .product-basic-info {
            .product-title {
                font-size: 18px;
            }

            .price-section {
                .current-price {
                    .amount {
                        font-size: 28px;
                    }
                }
            }
        }

        .action-buttons {
            flex-direction: column;

            button {
                height: 44px;
            }
        }
    }
}

.link-info {
    gap: 5px;

    div {
        font-weight: 550;
        color: #313033;
        font-size: 12px;
    }
}
</style>
