<template>
    <div class="supply-demand-page">
        <!-- 搜索筛选区域 -->
        <div class="search-filter-section">
            <AiSearch placeholder="请输入您的查询需求，AI自动为您匹配供需"  @search="handleAISearch"/>
        </div>

        <!--  分类区域  -->
        <div class="filter-box flex-row jc-space-between ai-center">
            <category :list="cateList" @cate="handleCate"></category>
        </div>

        <div class="supply-demand-list" v-loading="loading">
            <div class="list-container">
                <supply-demand-item
                    v-for="item in dataList"
                    :key="item.id"
                    :item="item"
                    @item-click="handleSupplyClick"
                >
                </supply-demand-item>
            </div>

            <!-- 空状态 -->
            <el-empty
                v-if="!loading && dataList.length === 0"
                description="暂无供需数据"
            ></el-empty>

            <!-- 加载更多提示 -->
            <load-more-tip
                :data-length="dataList.length"
                :loading="isLoadingMore"
                :has-more="hasMoreData"
            />
        </div>
    </div>

    <!--  供需详情的弹窗  -->
    <supply-detail-modal ref="supplyDemandModalRef" :supplys="selectedSupply"></supply-detail-modal>
</template>

<script setup>
import SupplyDemandItem from '@/components/SupplyDemandItem/SupplyDemandItem.vue'
import { useMessage } from '@/utils/useMessage.js'
import { supplyCateUpList, supplyDemandDetail, supplyDemandList } from '@/api/supply-demand.js'
import { useInfiniteScroll } from '@/composables/useInfiniteScroll.js'

import AiSearch from '@/components/AiSearch/AiSearch.vue'
import LoadMoreTip from '@/components/LoadMoreTip/LoadMoreTip.vue'
import Category from '@/components/Category/Category.vue'
import SupplyDetailModal from '@/views/trade/components/SupplyDetailModal.vue'
import { aiSearch } from '@/api/main.js'

// 响应式数据
const loading = ref(false)
const dataList = ref([])
const total = ref(0)
const cateList = ref([
    {
        id: null,
        icon: 'quanbu',
        name: '全部',
    },
])
const supplyDemandModalRef = ref()
const selectedSupply = ref({})

// 搜索表单
const searchForm = reactive({
    title: '', // 商品名称
    cateId: '', // 分类ID
})

// 分页参数
const pagination = reactive({
    current: 1,
    size: 10, // 每页20条，适合下拉加载
})

// 当前查询是不是AI 查询
const isAIQuery = ref(false)
const searchName = ref('')

// 下拉加载相关状态
const hasMoreData = ref(true)
const isLoadingMore = ref(false) // 防止重复加载的标志
// 获取供需列表
const fetchGoodsList = async (isLoadMore = false) => {
    try {
        loading.value = true

        // 构建查询参数
        const params = {
            current: pagination.current,
            size: pagination.size,
            ...searchForm,
        }

        if (isAIQuery.value) {
            params.type = 3 // 	查询类型 1.产品 2.服务 3.供需 4.企业
            params.query = searchName.value
        }

        // 清理空值参数
        Object.keys(params).forEach((key) => {
            if (
                params[key] === null ||
                params[key] === '' ||
                (Array.isArray(params[key]) && params[key].length === 0)
            ) {
                delete params[key]
            }
        })

        console.log('请求参数:', params)
        // 尝试调用真实接口，如果失败则使用模拟数据
        let response
        try {
            if (isAIQuery.value) {
                response = await aiSearch(params)
                console.log('AI搜索结果', response)
            } else {
                response = await supplyDemandList(params)
            }
        } catch (error) {
            useMessage().error(error.msg || '获取供需列表失败')
            // 确保在错误时也重置加载状态
            loading.value = false
            return
        }

        if (response.code === 0) {
            const newData = response.data.records || []

            if (isLoadMore) {
                // 下拉加载：追加数据
                dataList.value = [...dataList.value, ...newData]
            } else {
                // 首次加载或搜索：替换数据
                dataList.value = newData
            }

            total.value = response.data.total || 0

            // 只在首次加载时更新分页信息
            if (!isLoadMore) {
                pagination.current = response.data.current || 1
                pagination.size = response.data.size || 20
            }

            // 检查是否还有更多数据
            hasMoreData.value = dataList.value.length < total.value
        } else {
            useMessage().error(response.msg || '获取供需列表失败')
            if (!isLoadMore) {
                dataList.value = []
                total.value = 0
            }
        }
    } catch (error) {
        console.error('获取供需列表失败:', error)
        useMessage().error('获取供需列表失败')
        if (!isLoadMore) {
            dataList.value = []
            total.value = 0
        }
    } finally {
        loading.value = false
    }
}

// 搜索处理
const handleSearch = () => {
    // 重置分页和状态
    pagination.current = 1
    hasMoreData.value = true
    // 重新获取数据（替换而不是追加）
    fetchGoodsList(false)
}

// 重置数据
const resetData = () => {
    pagination.current = 1
    dataList.value = []
    total.value = 0
    hasMoreData.value = true
}

// 加载更多数据的函数
const loadMoreData = async () => {
    // 防止重复加载
    if (!hasMoreData.value || isLoadingMore.value || loading.value) {
        return
    }

    try {
        isLoadingMore.value = true
        pagination.current += 1
        await fetchGoodsList(true)
    } finally {
        isLoadingMore.value = false
    }
}

// 设置无限滚动
const { loading: infiniteScrollLoading } = useInfiniteScroll({
    loadMore: loadMoreData,
    hasMore: hasMoreData,
    threshold: 100,
})

// 加载供需分类，只显示一级
const loadGXCate = async () => {
    try {
        // const response = await supplyDemandCate({ pid: 0 })
        const response = await supplyCateUpList({ pid: 0 })
        cateList.value = [...cateList.value, ...response.data]
        console.log('供需分类', cateList.value)
    } catch (error) {
        console.error('获取供需分类失败:', error)
    }
}

// 分类搜索
const handleCate = (item) => {
    searchForm.cateId = item ? item.id : ''
    handleSearch()
}

const handleSupplyClick = (item) => {
    console.log('点击供需:', item)
    // 一定要用showModal，不然图片会显示异常，不会测试的话，永远不会复现，嘿嘿
    nextTick(async () => {
        const { data } = await supplyDemandDetail({ id: item.id })
        console.log('data', data)
        // 丰富商品数据
        selectedSupply.value = {
            ...item,
            ...data,
        }
        supplyDemandModalRef.value.openModal(selectedSupply)
    })
}

const handleAISearch = async (keyWord) => {
    console.log('搜索关键词', keyWord)

    // 如果有 keyWord  ，则是AI查询，否则是普通查询
    isAIQuery.value = !!keyWord
    searchName.value = keyWord
    pagination.current = 1

    handleSearch()
}

// 组件挂载时获取数据
onMounted(() => {
    loadGXCate()
    fetchGoodsList()
})

// keep-alive 组件激活时的处理
onActivated(() => {
    // 可以在这里添加数据刷新逻辑，如果需要的话
    // 例如：检查数据是否过期，是否需要重新获取
})

// keep-alive 组件失活时的处理
onDeactivated(() => {
    // 重置加载状态，防止下次激活时状态异常
    isLoadingMore.value = false
})
</script>

<style scoped lang="scss">
.supply-demand-page {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: calc(100vh - 60px);

    // 搜索筛选区域
    .search-filter-section {
        margin-bottom: 20px;
    }

    .filter-box {
        margin-bottom: 15px;
    }

    .supply-demand-list {
        background-color: #f5f5f5;
    }

    .list-container {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 16px;
        margin: 0 auto;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .list-container {
            grid-template-columns: 1fr;
            gap: 12px;
        }

        .supply-demand-list {
            padding: 12px;
        }

        .supply-demand-item {
            height: auto;
            min-height: 100px;
        }

        .item-image {
            width: 120px;
            height: 100px;
        }
    }

    @media (max-width: 480px) {
        .supply-demand-item {
            flex-direction: column;
            height: auto;
        }

        .item-image {
            width: 100%;
            height: 120px;
        }

        .item-content {
            padding: 12px;
        }
    }
}
</style>
