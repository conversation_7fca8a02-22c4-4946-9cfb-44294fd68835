<template>
    <div class="user-profile">
        <!-- 用户信息卡片 -->
        <div class="user-info-card p-24 mb-24">
            <div class="flex-row ai-center user-info-header">
                <!-- 头像 -->
                <div class="avatar-section">
                    <div class="avatar-circle">
                        <el-image
                            :src="
                                (userStore.userInfo && userStore.userInfo.avatar) || defaultAvatar
                            "
                            :alt="(userStore.userInfo && userStore.userInfo.nickname) || '默认头像'"
                        ></el-image>
                    </div>
                </div>

                <!-- 用户基本信息 -->
                <div class="user-basic-info flex-1">
                    <div class="flex-row ai-center mb-8">
                        <h2 class="text-24" style="color: #333; font-weight: 600; margin: 0">
                            {{ userStore.userInfo.nickname || '未登录' }}
                        </h2>
                    </div>
                    <el-button
                        type="primary"
                        text
                        size="small"
                        @click="editProfile"
                        v-if="userStore.userInfo"
                    >
                        修改个人信息 >
                    </el-button>
                </div>

                <!-- 安全信息 -->
                <div class="security-info">
                    <div class="security-item mb-8">
                        <span class="text-14" style="color: #666">账户安全：</span>
                        <span class="text-14" style="color: #27ae60">较高</span>
                    </div>
                    <div class="security-item mb-8">
                        <span class="text-14" style="color: #666">绑定手机：</span>
                        <span class="text-14" style="color: #333">{{
                            userStore.userInfo.phone
                        }}</span>
                    </div>
                    <div class="security-item">
                        <span class="text-14" style="color: #666">绑定邮箱：</span>
                        <span class="text-14" style="color: #333">{{
                            userStore.userInfo.email || '暂未绑定'
                        }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能统计卡片 -->
        <div class="stats-grid">
            <!-- 待支付订单 -->
            <div class="stat-card p-20" @click="goToOrders('1')">
                <div class="flex-row ai-center">
                    <div class="stat-icon mr-16">
                        <el-image :src="my_zhifu_ico"></el-image>
                    </div>
                    <div class="stat-content flex-1">
                        <div class="stat-number text-20" style="color: #ff6b35; font-weight: 600">
                            {{ orderInfo.waitPayCount }}
                        </div>
                        <div class="stat-label text-14" style="color: #666">待支付的订单</div>
                        <div class="stat-action text-12" style="color: #999">查看订单 ></div>
                    </div>
                </div>
            </div>

            <!-- 待发货或待接单数量 -->
            <div class="stat-card p-20" @click="goToOrders('2')">
                <div class="flex-row ai-center">
                    <div class="stat-icon mr-16">
                        <el-image :src="my_daifahuo_ico"></el-image>
                    </div>
                    <div class="stat-content flex-1">
                        <div class="stat-number text-20" style="color: #8bc34a; font-weight: 600">
                            {{ orderInfo.waitSendCount }}
                        </div>
                        <div class="stat-label text-14" style="color: #666">待发货/接单的订单</div>
                        <div class="stat-action text-12" style="color: #999">查看订单 ></div>
                    </div>
                </div>
            </div>

            <!-- 待发货或待接单数量 -->
            <div class="stat-card p-20" @click="goToOrders('3')">
                <div class="flex-row ai-center">
                    <div class="stat-icon mr-16">
                        <el-image :src="my_daishouhuo_ico"></el-image>
                    </div>
                    <div class="stat-content flex-1">
                        <div class="stat-number text-20" style="color: #8bc34a; font-weight: 600">
                            {{ orderInfo.waitReceiveCount }}
                        </div>
                        <div class="stat-label text-14" style="color: #666">待收货/确认的订单</div>
                        <div class="stat-action text-12" style="color: #999">查看订单 ></div>
                    </div>
                </div>
            </div>

            <!-- 服务中数量 -->
            <div class="stat-card p-20" @click="goToOrders('4')">
                <div class="flex-row ai-center">
                    <div class="stat-icon mr-16">
                        <el-image :src="my_yiwancheng_ico"></el-image>
                    </div>
                    <div class="stat-content flex-1">
                        <div class="stat-number text-20" style="color: #8bc34a; font-weight: 600">
                            {{ orderInfo.receiveCount }}
                        </div>
                        <div class="stat-label text-14" style="color: #666">服务中的订单</div>
                        <div class="stat-action text-12" style="color: #999">查看订单 ></div>
                    </div>
                </div>
            </div>

            <!-- 售后数量 -->
            <div class="stat-card p-20" @click="goToOrders('5')">
                <div class="flex-row ai-center">
                    <div class="stat-icon mr-16">
                        <el-image :src="my_after_ico"></el-image>
                    </div>
                    <div class="stat-content flex-1">
                        <div class="stat-number text-20" style="color: #8bc34a; font-weight: 600">
                            {{ orderInfo.waitRefundCount }}
                        </div>
                        <div class="stat-label text-14" style="color: #666">售后的订单</div>
                        <div class="stat-action text-12" style="color: #999">查看订单 ></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import defaultAvatar from '@/assets/default_avatar.png'
import { useUserStore } from '@/stores/user.js'
import { countOrder } from '@/api/order.js'

import my_daifahuo_ico from '@/assets/my_daifahuo_ico.png'
import my_daishouhuo_ico from '@/assets/my_daishouhuo_ico.png'
import my_yiwancheng_ico from '@/assets/my_yiwancheng_ico.png'
import my_zhifu_ico from '@/assets/my_zhifu_ico.png'
import my_after_ico from '@/assets/my_after_ico.png'

const router = useRouter()
const userStore = useUserStore()
const orderInfo = reactive({
    waitPayCount: 0, // 待付款数量
    waitSendCount: 0, // 待发货或待接单数量
    waitReceiveCount: 0, // 待收货或待确认数量
    receiveCount: 0, // 服务中数量
    waitRefundCount: 0, // 售后数量
})

// 编辑个人信息
const editProfile = () => {
    console.log('编辑个人信息')
    // 这里可以跳转到编辑页面或打开编辑弹窗
}

// 跳转到订单页面
const goToOrders = (status) => {
    router.push({
        path: '/mine/order',
        query: { status },
    })
}

// 跳转到评价页面
const goToReviews = () => {
    console.log('跳转到待评价页面')
    // router.push('/reviews')
}

// 跳转到收藏页面
const goToFavorites = () => {
    console.log('跳转到收藏页面')
    // router.push('/favorites')
}

const loadCountOrder = async () => {
    try {
        // 写死不要分页
        const { data } = await countOrder()
        Object.assign(orderInfo, data)
    } catch (error) {}
}

onMounted(() => {
    console.log('用户信息', userStore.userInfo)
    loadCountOrder()
})
</script>

<style lang="scss" scoped>
.user-profile {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;

    .user-info-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .user-info-header {
            gap: 20px;

            .avatar-section {
                .avatar-circle {
                    width: 80px;
                    height: 80px;
                    border-radius: 50%;
                    background: #f0f0f0;

                    .el-image {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }

                    .avatar-text {
                        color: #999;
                        font-weight: 600;
                    }
                }
            }

            .security-info {
                min-width: 200px;
            }
        }
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;

        .stat-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }

            .stat-icon {
                width: 48px;
                height: 48px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-shrink: 0;
            }

            .stat-content {
                .stat-number {
                    line-height: 1.2;
                    margin-bottom: 4px;
                }

                .stat-label {
                    line-height: 1.2;
                    margin-bottom: 4px;
                }

                .stat-action {
                    line-height: 1.2;
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .user-profile {
        padding: 16px;

        .user-info-card {
            .flex-row {
                flex-direction: column;
                align-items: flex-start !important;
                gap: 16px;

                .avatar-section {
                    margin-right: 0 !important;
                    align-self: center;
                }

                .security-info {
                    min-width: auto;
                    width: 100%;
                }
            }
        }

        .stats-grid {
            grid-template-columns: 1fr;
            gap: 12px;

            .stat-card {
                .flex-row {
                    flex-direction: column;
                    align-items: center !important;
                    text-align: center;
                    gap: 12px;

                    .stat-icon {
                        margin-right: 0 !important;
                    }
                }
            }
        }
    }
}
</style>
