<template>
    <el-dialog
        v-model="visible"
        :destroy-on-close="true"
        title="订单详情"
        width="900px"
        top="5vh"
        :before-close="handleClose"
        class="order-detail-dialog"
    >
        <el-scrollbar>
            <div v-if="orderData" class="order-detail-content">
                <!-- 收货信息 -->
                <div v-if="orderData.receiveName" class="address-section mb-20">
                    <div class="section-title mb-15">收货信息</div>
                    <div class="address-card p-16">
                        <div class="flex-row ai-center mb-8">
                            <span class="receiver-name text-16">{{ orderData.receiveName }}</span>
                            <span class="receiver-phone ml-16 text-14" style="color: #666">
                                {{ orderData.receivePhone }}
                            </span>
                            <span
                                v-if="orderData.receivePhoneX"
                                class="receiver-phone-x ml-8 text-12"
                                style="color: #999"
                            >
                                (X号码: {{ orderData.receivePhoneX }})
                            </span>
                        </div>
                        <div class="address-detail text-14" style="color: #666">
                            {{ orderData.receiveAddress }}
                        </div>
                        <div v-if="orderData.selfLifting" class="self-lifting-info mt-8">
                            <el-tag type="info" size="small">自提订单</el-tag>
                            <span
                                v-if="orderData.selfLiftingTime"
                                class="ml-8 text-12"
                                style="color: #666"
                            >
                                自提时间：{{ orderData.selfLiftingTime }}
                            </span>
                        </div>
                        <div v-if="orderData.verifyCode" class="verify-code mt-8">
                            <span class="text-12" style="color: #666">核销码：</span>
                            <span class="text-14 verify-code-text">{{ orderData.verifyCode }}</span>
                        </div>
                    </div>
                </div>

                <!-- 订单基本信息 -->
                <div class="order-info-section mb-20">
                    <div class="section-title mb-15">订单信息</div>
                    <div class="info-grid">
                        <div class="info-item flex-row ai-center">
                            <span class="label">订单号：</span>
                            <span class="value">{{ orderData.orderNo }}</span>
                        </div>
                        <div class="info-item flex-row ai-center">
                            <span class="label">交易单号：</span>
                            <span class="value">{{ orderData.payOrderNo || '未生成' }}</span>
                        </div>
                        <div class="info-item flex-row ai-center">
                            <span class="label">下单时间：</span>
                            <span class="value">{{
                                orderData.buyTime || orderData.createTime
                            }}</span>
                        </div>
                        <div class="info-item flex-row ai-center">
                            <span class="label">支付时间：</span>
                            <span class="value">{{ orderData.payTime || '未支付' }}</span>
                        </div>
                        <div class="info-item flex-row ai-center">
                            <span class="label">订单状态：</span>
                            <el-tag :type="getStatusType(orderData.status)">
                                {{ getStatusText(orderData.status) }}
                            </el-tag>
                        </div>
                        <div class="info-item flex-row ai-center">
                            <span class="label">支付方式：</span>
                            <span class="value">{{ getPayMethodText(orderData.payMethod) }}</span>
                        </div>
                    </div>
                </div>

                <!-- 费用明细 -->
                <div class="cost-section mb-20">
                    <div class="section-title mb-15">费用明细</div>
                    <div class="cost-details">
                        <div class="cost-item flex-row ai-center jc-space-between">
                            <span class="label">商品总价：</span>
                            <span class="value">¥{{ orderData.totalPrice || 0 }}</span>
                        </div>
                        <div class="cost-item flex-row ai-center jc-space-between">
                            <span class="label">运费：</span>
                            <span class="value">¥{{ orderData.freightFee || 0 }}</span>
                        </div>
                        <div
                            v-if="orderData.serviceFee"
                            class="cost-item flex-row ai-center jc-space-between"
                        >
                            <span class="label">服务费：</span>
                            <span class="value">¥{{ orderData.serviceFee }}</span>
                        </div>
                        <div
                            v-if="orderData.couponAmount"
                            class="cost-item flex-row ai-center jc-space-between"
                        >
                            <span class="label">优惠券抵扣：</span>
                            <span class="value discount">-¥{{ orderData.couponAmount }}</span>
                        </div>
                        <div class="cost-item total flex-row ai-center jc-space-between">
                            <span class="label">实付金额：</span>
                            <span class="value price">¥{{ orderData.payPrice || 0 }}</span>
                        </div>
                        <div
                            v-if="orderData.refundAmount"
                            class="cost-item flex-row ai-center jc-space-between"
                        >
                            <span class="label">退款金额：</span>
                            <span class="value refund">¥{{ orderData.refundAmount }}</span>
                        </div>
                    </div>
                </div>

                <!-- 商品信息 -->
                <div class="products-section mb-20">
                    <div class="section-title mb-15 flex-row jc-space-between ai-center">
                        <div class="_left">商品信息</div>
                        <div class="_right flex-row ai-center">
                            <el-image :src="orderData.companyInfoVo.logo"></el-image>
                            <div class="_name">
                                {{ orderData.companyInfoVo.name }}
                            </div>
                        </div>
                    </div>
                    <div class="products-table">
                        <div class="table-header flex-row ai-center">
                            <div class="col-product">商品信息</div>
                            <div class="col-price text-center">原价</div>
                            <div class="col-price text-center">单价</div>
                            <div class="col-quantity text-center">数量</div>
                            <div class="col-total text-center">小计</div>
                        </div>
                        <div
                            v-for="item in orderData['detailList']"
                            :key="item.id"
                            class="table-row flex-row ai-center"
                        >
                            <div class="col-product flex-row ai-center">
                                <img
                                    :src="item.goodsMainImg || '/default-product.png'"
                                    :alt="item.goodsName"
                                    class="product-image"
                                />
                                <div class="product-info ml-12">
                                    <div class="product-name text-14">{{ item.goodsName }}</div>
                                </div>
                            </div>
                            <div class="col-price text-center">
                                <span class="original-price">¥{{ item.buyOriPrice }}</span>
                            </div>
                            <div class="col-price text-center">
                                <span class="price">¥{{ item.buyPrice }}</span>
                            </div>
                            <div class="col-quantity text-center">
                                <span>{{ item.buyNum }}</span>
                            </div>
                            <div class="col-total text-center">
                                <span class="price">¥{{ item.buyTotalPrice }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 物流信息 -->
                <div
                    v-if="orderData.deliveryType || orderData.deliveryNo"
                    class="logistics-section mb-20"
                >
                    <div class="section-title mb-15">物流信息</div>
                    <div class="logistics-card p-16">
                        <div class="flex-row ai-center jc-space-between mb-12">
                            <div>
                                <span class="text-14">配送方式：</span>
                                <span class="text-14">{{
                                    orderData.deliveryType || '未设置'
                                }}</span>
                            </div>
                            <div v-if="orderData.deliveryNo">
                                <span class="text-14">配送单号：</span>
                                <span class="text-14 logistics-no">{{ orderData.deliveryNo }}</span>
                            </div>
                        </div>
                        <div class="logistics-times">
                            <div
                                v-if="orderData.deliveryTime"
                                class="time-item text-12"
                                style="color: #666"
                            >
                                发货时间：{{ orderData.deliveryTime }}
                            </div>
                            <div
                                v-if="orderData.finishTime"
                                class="time-item text-12"
                                style="color: #666"
                            >
                                完成时间：{{ orderData.finishTime }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 订单备注 -->
                <div v-if="orderData.remark" class="remark-section">
                    <div class="section-title mb-15">订单备注</div>
                    <div class="remark-content p-16">
                        {{ orderData.remark }}
                    </div>
                </div>
            </div>

            <!-- 加载状态 -->
            <div
                v-else
                class="loading-container flex-column ai-center jc-center"
                style="height: 400px"
            >
                <el-icon class="is-loading" size="24">
                    <Loading />
                </el-icon>
                <span class="mt-12 text-14" style="color: #666">加载中...</span>
            </div>
        </el-scrollbar>

        <template #footer>
            <div class="dialog-footer flex-row jc-flex-end">
                <el-button @click="handleClose">关闭</el-button>
                <el-button v-if="canCancel" type="danger" @click="handleCancelOrder">
                    取消订单
                </el-button>
                <el-button v-if="canPay" type="primary" @click="handlePayOrder">
                    立即支付
                </el-button>
                <el-button v-if="canConfirm" type="success" @click="handleConfirmOrder">
                    确认收货
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { Loading } from '@element-plus/icons-vue'
import { orderDetail } from '@/api/order.js'
import { useMessage } from '@/utils/useMessage.js'

// Props
const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false,
    },
    orderId: {
        type: [String, Number],
        default: null,
    },
})

// Emits
const emit = defineEmits(['update:modelValue', 'refresh'])

// 响应式数据
const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value),
})

const orderData = ref(null)
const loading = ref(false)

// 订单状态映射
const statusMap = {
    0: { text: '待付款', type: 'warning' },
    1: { text: '待发货', type: 'info' },
    2: { text: '待收货', type: 'primary' },
    3: { text: '已完成', type: 'success' },
    4: { text: '已取消', type: 'danger' },
    5: { text: '退款中', type: 'warning' },
    6: { text: '已退款', type: 'info' },
    7: { text: '部分退款', type: 'warning' },
}

// 支付方式映射
const payMethodMap = {
    1: '微信支付',
    2: '支付宝',
    3: '银行卡',
    4: '余额支付',
    5: '货到付款',
    6: '线下支付',
}

// 获取状态文本
const getStatusText = (status) => {
    return statusMap[status]?.text || '未知状态'
}

// 获取状态类型
const getStatusType = (status) => {
    return statusMap[status]?.type || 'info'
}

// 获取支付方式文本
const getPayMethodText = (payMethod) => {
    return payMethodMap[payMethod] || '未设置'
}

// 计算属性：是否可以取消订单
const canCancel = computed(() => {
    return orderData.value && ['pending', 'paid'].includes(orderData.value.status)
})

// 计算属性：是否可以支付
const canPay = computed(() => {
    return orderData.value && orderData.value.status === 'pending'
})

// 计算属性：是否可以确认收货
const canConfirm = computed(() => {
    return orderData.value && orderData.value.status === 'shipped'
})

// 获取订单详情
const fetchOrderDetail = async () => {
    if (!props.orderId) return

    try {
        loading.value = true
        const response = await orderDetail({ orderId: props.orderId })

        if (response.code === 0) {
            orderData.value = response.data
        } else {
            useMessage().error(response.msg || '获取订单详情失败')
        }
    } catch (error) {
        console.error('获取订单详情失败:', error)
        useMessage().error('获取订单详情失败')
    } finally {
        loading.value = false
    }
}

// 关闭弹窗
const handleClose = () => {
    visible.value = false
    orderData.value = null
}

// 取消订单
const handleCancelOrder = async () => {
    try {
        // 这里调用取消订单的 API
        useMessage().success('订单取消成功')
        emit('refresh')
        handleClose()
    } catch (error) {
        console.error('取消订单失败:', error)
        useMessage().error('取消订单失败')
    }
}

// 支付订单
const handlePayOrder = async () => {
    try {
        // 这里调用支付订单的 API
        useMessage().success('跳转支付页面')
        // 实际项目中这里应该跳转到支付页面
    } catch (error) {
        console.error('支付失败:', error)
        useMessage().error('支付失败')
    }
}

// 确认收货
const handleConfirmOrder = async () => {
    try {
        // 这里调用确认收货的 API
        useMessage().success('确认收货成功')
        emit('refresh')
        handleClose()
    } catch (error) {
        console.error('确认收货失败:', error)
        useMessage().error('确认收货失败')
    }
}

// 监听弹窗显示状态
watch(
    () => props.modelValue,
    (newVal) => {
        if (newVal && props.orderId) {
            fetchOrderDetail()
        }
    },
)
</script>

<style scoped lang="scss">
.order-detail-dialog {
    .order-detail-content {
        max-height: 75vh;

        .section-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            border-bottom: 1px solid #409eff;
            padding-bottom: 8px;

            ._left {
                font-size: 14px;
                font-weight: 600;
                color: #333;
            }

            ._right {
                gap: 10px;

                .el-image {
                    width: 32px;
                    height: 32px;
                    object-fit: contain;
                    border-radius: 5px;
                }

                ._name {
                    font-size: 12px;
                    color: #999;
                }
            }
        }

        // 订单信息网格
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;

            .info-item {
                .label {
                    font-size: 14px;
                    color: #666;
                    min-width: 80px;
                }

                .value {
                    font-size: 14px;
                    color: #333;

                    &.price {
                        color: #e74c3c;
                        font-weight: 600;
                    }
                }
            }
        }

        // 费用明细
        .cost-details {
            background: #f8f9fa;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            padding: 16px;

            .cost-item {
                padding: 8px 0;
                border-bottom: 1px solid #f0f0f0;

                &:last-child {
                    border-bottom: none;
                }

                &.total {
                    margin-top: 8px;
                    padding-top: 12px;
                    font-weight: 600;

                    .value {
                        font-size: 16px;
                    }
                }

                .label {
                    font-size: 14px;
                    color: #666;
                }

                .value {
                    font-size: 14px;
                    color: #333;

                    &.price {
                        color: #e74c3c;
                        font-weight: 600;
                    }

                    &.discount {
                        color: #27ae60;
                    }

                    &.refund {
                        color: #f39c12;
                    }
                }
            }
        }

        // 商品表格
        .products-table {
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            overflow: hidden;

            .table-header {
                background: #f8f9fa;
                border-bottom: 1px solid #e8e8e8;
                font-weight: 600;
                color: #333;

                > div {
                    padding: 12px;
                    font-size: 14px;
                }
            }

            .table-row {
                border-bottom: 1px solid #f0f0f0;

                &:last-child {
                    border-bottom: none;
                }

                > div {
                    padding: 16px 12px;
                }
            }

            .col-product {
                flex: 2;
                min-width: 200px;

                .product-image {
                    width: 60px;
                    height: 60px;
                    object-fit: cover;
                    border-radius: 4px;
                    border: 1px solid #e8e8e8;
                }

                .product-info {
                    flex: 1;

                    .product-name {
                        font-weight: 500;
                        color: #333;
                        line-height: 1.4;
                    }
                }
            }

            .original-price {
                color: #999;
                text-decoration: line-through;
                font-size: 12px;
            }

            .col-spec {
                flex: 1;
                min-width: 100px;
            }

            .col-price,
            .col-quantity,
            .col-total {
                flex: 0.8;
                min-width: 80px;

                .price {
                    color: #e74c3c;
                    font-weight: 600;
                }
            }
        }

        // 收货地址卡片
        .address-card {
            background: #f8f9fa;
            border: 1px solid #e8e8e8;
            border-radius: 6px;

            .receiver-name {
                font-weight: 600;
                color: #333;
            }

            .verify-code-text {
                font-family: 'Courier New', monospace;
                color: #409eff;
                font-weight: 600;
                background: #ecf5ff;
                padding: 2px 8px;
                border-radius: 4px;
            }
        }

        // 企业信息卡片
        .company-card {
            background: #f8f9fa;
            border: 1px solid #e8e8e8;
            border-radius: 6px;

            .company-logo {
                width: 60px;
                height: 60px;
                object-fit: cover;
                border-radius: 6px;
                border: 1px solid #e8e8e8;
            }

            .company-name {
                font-weight: 600;
                color: #333;
            }

            .detail-item {
                .label {
                    min-width: 120px;
                    flex-shrink: 0;
                }

                .value {
                    flex: 1;
                    word-break: break-all;
                }
            }
        }

        // 物流信息卡片
        .logistics-card {
            background: #f8f9fa;
            border: 1px solid #e8e8e8;
            border-radius: 6px;

            .logistics-no {
                font-family: 'Courier New', monospace;
                color: #409eff;
                font-weight: 600;
                background: #ecf5ff;
                padding: 2px 8px;
                border-radius: 4px;
            }

            .logistics-times {
                margin-top: 8px;

                .time-item {
                    margin-bottom: 4px;

                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }
        }

        // 备注内容
        .remark-content {
            background: #f8f9fa;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            color: #666;
            font-size: 14px;
            line-height: 1.6;
        }
    }

    // 加载状态
    .loading-container {
        .el-icon {
            color: #409eff;
        }
    }

    // 底部按钮
    .dialog-footer {
        gap: 12px;
    }
}

// 响应式设计
@media (max-width: 768px) {
    .order-detail-dialog {
        .order-detail-content {
            .info-grid {
                grid-template-columns: 1fr;
            }

            .products-table {
                .table-header,
                .table-row {
                    flex-direction: column;

                    > div {
                        text-align: left !important;
                        border-bottom: 1px solid #f0f0f0;

                        &:last-child {
                            border-bottom: none;
                        }
                    }
                }

                .col-product {
                    min-width: auto;
                }
            }
        }
    }
}
</style>
