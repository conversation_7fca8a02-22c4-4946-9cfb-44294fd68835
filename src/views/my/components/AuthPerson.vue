<template>
    <el-dialog
        v-model="dialogVisible"
        :destroy-on-close="true"
        title="个人实名认证"
        width="700px"
        :close-on-click-modal="false"
    >
        <div class="authentication-personal">
            <!-- 认证提示 -->
            <div class="auth-notice">
                <el-alert title="认证须知" type="warning" :closable="false" show-icon>
                    <p>1. 根据国家规定，未满14周岁的用户无法完成实名认证</p>
                    <p>2. 请确保填写的信息与身份证件完全一致</p>
                    <p>3. 认证信息仅用于身份核验，我们将严格保护您的隐私</p>
                </el-alert>
            </div>

            <!-- 认证表单 -->
            <div class="auth-form">
                <el-form
                    :model="formData"
                    :rules="formRules"
                    ref="formRef"
                    label-width="100px"
                    class="auth-rule-form"
                    label-position="top"
                    :disabled="formData.status === 1"
                >
                    <!-- 真实姓名 -->
                    <el-form-item label="真实姓名" prop="realName">
                        <el-input
                            v-model="formData.realName"
                            placeholder="请输入身份证上的真实姓名"
                            clearable
                            maxlength="30"
                            show-word-limit
                        ></el-input>
                    </el-form-item>

                    <!-- 证件类型 -->
                    <el-form-item label="证件类型" prop="idType">
                        <el-select
                            v-model="formData.idType"
                            placeholder="请选择证件类型"
                            style="width: 100%"
                            clearable
                        >
                            <template v-for="(item, itemIndex) in idTypeOptions">
                                <el-option :value="item.value" :label="item.label"></el-option>
                            </template>
                        </el-select>
                    </el-form-item>

                    <!-- 证件号码 -->
                    <el-form-item label="证件号码" prop="idCard" :rules="idCardberRules">
                        <el-input
                            v-model="formData.idCard"
                            placeholder="请输入证件号码"
                            clearable
                            :maxlength="20"
                        ></el-input>
                    </el-form-item>

                    <!-- 提交按钮 -->
                    <el-form-item v-if="formData.status !== 1">
                        <el-button
                            type="primary"
                            style="width: 100%"
                            @click="handleSubmit"
                            :loading="isSubmitting"
                        >
                            立即认证
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
        </div>
    </el-dialog>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { userAuthSubmit } from '@/api/authentication'

const dialogVisible = ref(false)
const formRef = ref()
const isSubmitting = ref(false)

watch(dialogVisible, (newVal) => {
    if (!newVal) {
        formRef.value?.resetFields()
    }
})

// 转换为选项数组
const idTypeOptions = ref([
    { value: 1, label: '中国大陆二代居民身份证' },
    { value: 2, label: '港澳居民来往内地通行证' },
    { value: 3, label: '台湾居民来往内地通行证' },
    { value: 4, label: '港澳居民居住证' },
    { value: 5, label: '台湾居民居住证' },
    { value: 6, label: '外国人永久居留证' },
    { value: 7, label: '其他' },
])

// 打开对话框
const openDialog = (item) => {
    dialogVisible.value = true
    // 重置表单状态
    Object.assign(formData, {
        id: null,
        type: 1, // 认证类型 1个人认证 2企业认证
        companyName: '', // 企业名称
        uscc: '', // 统一社会信用代码
        businessImg: '', // 营业执照
        realName: '', // 真实姓名或法人姓名
        idType: 1, // 证件类型
        idCard: '', // 证件号码
    })

    if (item) {
        Object.assign(formData, item)
    }
}

// 表单数据
const formData = reactive({
    id: null,
    type: 1, // 认证类型 1个人认证 2企业认证
    companyName: '', // 企业名称
    uscc: '', // 统一社会信用代码
    businessImg: '', // 营业执照
    realName: '', // 真实姓名或法人姓名
    idType: 1, // 证件类型
    idCard: '', // 证件号码
})

// 证件号码验证规则
const idCardberRules = computed(() => {
    const baseRules = [{ required: true, message: '请输入证件号码', trigger: 'blur' }]

    if (formData.idType === 1) {
        return [
            ...baseRules,
            {
                pattern:
                    /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/,
                message: '请输入有效的身份证号码',
                trigger: 'blur',
            },
        ]
    }
    return baseRules
})

// 表单验证规则
const formRules = reactive({
    realName: [
        { required: true, message: '请输入真实姓名', trigger: 'blur' },
        { min: 2, max: 30, message: '姓名长度为2-30个字符', trigger: 'blur' },
        { pattern: /^[\u4e00-\u9fa5]+$/, message: '请输入中文姓名', trigger: 'blur' },
    ],
    idType: [{ required: true, message: '请选择证件类型', trigger: 'change' }],
})

// 提交表单
const handleSubmit = async () => {
    try {
        await formRef.value.validate()
        isSubmitting.value = true

        // 模拟API调用
        // const result = await submitAuth(formData)
        const data = await userAuthSubmit(formData)

        console.log('data', data)

        ElMessage.success('实名认证提交成功')
        dialogVisible.value = false
    } catch (error) {
        console.error('认证提交失败:', error)
    } finally {
        isSubmitting.value = false
    }
}

defineExpose({
    openDialog,
})
</script>

<style scoped lang="scss">
.authentication-personal {
    padding: 0 20px 20px;
    box-sizing: border-box;

    .auth-notice {
        margin-bottom: 24px;

        :deep(.el-alert__title) {
            font-size: 14px;
            margin-bottom: 8px;
        }

        p {
            margin: 4px 0;
            font-size: 13px;
            line-height: 1.5;
        }
    }

    .auth-form {
        .auth-rule-form {
            width: 100%;
            max-width: 600px;
            margin: 0 auto;

            :deep(.el-form-item__label) {
                font-weight: 500;
                padding-bottom: 8px;
                color: #333;
            }

            .el-button {
                margin-top: 16px;
                height: 44px;
                font-size: 16px;
            }
        }
    }
}
</style>
