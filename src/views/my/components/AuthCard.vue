<template>
    <div class="auth-card" :class="[`${type}-auth`]" @click="$emit('click')">
        <h3 class="card-title">{{ title }}</h3>
        <p class="card-description">{{ description }}</p>
        <div class="card-action">
            <span v-if="status === 1">已认证,查看详情</span>
            <span v-else-if="status === 3">认证中</span>
            <span v-else-if="status === 9">无效，重新认证</span>
            <span v-else>立即认证</span>
            <svg-icon name="you"></svg-icon>
        </div>
    </div>
</template>

<script setup>
import SvgIcon from '@/components/SvgIcon/SvgIcon.vue'

defineProps({
    type: {
        required: true,
        type: String
    },
    title: {
        required: true,
        type: String
    },
    description: {
        required: true,
        type: String
    },
    status: {
        type: Number || null,
        default: null
    }
})
</script>

<style lang="scss" scoped>
@use "sass:color"; // 首先引入颜色模块

.auth-card {
    border: 1px solid #ededed;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s ease;
    cursor: pointer;
    height: 100%;

    &:hover {
        transform: translateY(-4px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border-color: #e6a23c;
    }

    .card-title {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 8px;
        color: #333;
    }

    .card-description {
        color: #999;
        font-size: 14px;
        line-height: 1.5;
        flex-grow: 1;
    }

    .card-action {
        display: flex;
        align-items: center;
        color: #e6a23c;
        font-size: 14px;
        margin-top: 20px;
        transition: color 0.3s;

        .el-icon {
            margin-left: 4px;
            transition: transform 0.3s;
        }
    }

    &:hover .card-action {
        color: color.adjust(#e6a23c, $lightness: -10%); // 亮度降低10%

        .el-icon {
            transform: translateX(4px);
        }
    }
}
</style>
