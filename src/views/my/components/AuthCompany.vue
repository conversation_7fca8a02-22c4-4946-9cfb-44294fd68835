<template>
    <el-dialog
        v-model="dialogVisible"
        :destroy-on-close="true"
        title="企业实名认证"
        width="700px"
        top="5vh"
    >
        <div class="authentication-enterprise">
            <div class="auth-notice">
                <i class="el-icon-info"></i>
                <span
                    >请填写企业或组织的真实信息，包括企业名称、统一社会信用代码及法定代表人信息。</span
                >
            </div>

            <div class="auth-form">
                <el-scrollbar height="calc(90vh - 200px)">
                    <el-form
                        ref="formRef"
                        :model="formData"
                        :rules="formRules"
                        label-width="120px"
                        class="enterprise-form"
                        label-position="top"
                        :disabled="formData.status === 1"
                    >
                        <el-form-item label="营业执照" prop="businessImg">
                            <upload-image
                                :image-urls="imgList"
                                :max-size="3"
                                width="100px"
                                height="100px"
                                @update:image-urls="UploadEmits"
                            ></upload-image>
                        </el-form-item>

                        <el-form-item label="企业/组织名称" prop="companyName">
                            <el-input
                                v-model="formData.companyName"
                                placeholder="请输入营业执照上的全称"
                                clearable
                                maxlength="50"
                                show-word-limit
                            ></el-input>
                        </el-form-item>

                        <el-form-item label="统一社会信用代码" prop="uscc">
                            <el-input
                                v-model="formData.uscc"
                                placeholder="请输入18位统一社会信用代码"
                                clearable
                                maxlength="18"
                            ></el-input>
                        </el-form-item>

                        <el-form-item label="经营范围" prop="businessContent">
                            <el-input
                                v-model="formData.businessContent"
                                placeholder="请输入经营范围"
                                type="textarea"
                                clearable
                                resize="none"
                            ></el-input>
                        </el-form-item>

                        <el-form-item label="成立日期" prop="establishDate">
                            <el-date-picker
                                v-model="formData.establishDate"
                                type="date"
                                placeholder="成立日期"
                                format="YYYY年MM月DD日"
                                value-format="YYYY年MM月DD日"
                            ></el-date-picker>
                        </el-form-item>

                        <el-form-item label="注册资本" prop="registeredCapital">
                            <el-input
                                v-model="formData.registeredCapital"
                                placeholder="请输入注册资本"
                                clearable
                            ></el-input>
                        </el-form-item>

                        <el-form-item label="企业类型" prop="companyType">
                            <el-input
                                v-model="formData.companyType"
                                placeholder="请输入企业类型"
                                clearable
                            ></el-input>
                        </el-form-item>

                        <el-form-item label="法定代表人姓名" prop="realName">
                            <el-input
                                v-model="formData.realName"
                                placeholder="请输入法定代表人真实姓名"
                                clearable
                            ></el-input>
                        </el-form-item>
                    </el-form>
                </el-scrollbar>
            </div>
        </div>

        <template v-if="formData.status !== 1" #footer>
            <div style="padding: 0 30px">
                <el-button
                    type="primary"
                    style="width: 100%"
                    :loading="submitting"
                    @click="handleSubmit"
                >
                    提交认证
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { extractBusinessImg, userAuthSubmit } from '@/api/authentication'
import UploadImage from '@/components/UploadImage/UploadImage.vue'

const dialogVisible = ref(false)
const submitting = ref(false)
const formRef = ref()
const imgList = ref([])

// 打开对话框
const openDialog = (item) => {
    dialogVisible.value = true

    // 重置表单状态
    Object.assign(formData, {
        id: null,
        type: 2, // 认证类型 1个人认证 2企业认证
        companyName: '', // 企业名称
        uscc: '', // 统一社会信用代码
        businessImg: '', // 营业执照
        realName: '', // 真实姓名或法人姓名
        idType: 1, // 证件类型
        idCard: '', // 证件号码
        businessContent: '', // 经营范围
        registeredCapital: '', // 注册资本
        establishDate: '', // 成立日期
        companyType: '', // 企业类型
    })

    if (item) {
        Object.assign(formData, item)

        imgList.value = [formData.businessImg]
    }
}

// 表单数据
const formData = reactive({
    id: null,
    type: 2, // 认证类型 1个人认证 2企业认证
    companyName: '', // 企业名称
    uscc: '', // 统一社会信用代码
    businessImg: '', // 营业执照
    realName: '', // 真实姓名或法人姓名
    businessContent: '', // 经营范围
    registeredCapital: '', // 注册资本
    establishDate: '', // 成立日期
    companyType: '', // 企业类型
})

// 表单验证规则
const formRules = reactive({
    companyName: [
        { required: true, message: '请输入企业名称', trigger: 'blur' },
        { min: 2, max: 50, message: '企业名称长度为2-50个字符', trigger: 'blur' },
    ],
    uscc: [
        { required: true, message: '请输入统一社会信用代码', trigger: 'blur' },
        { pattern: /^[0-9A-Z]{18}$/, message: '请输入正确的18位统一社会信用代码', trigger: 'blur' },
    ],
    realName: [
        { required: true, message: '请输入法定代表人姓名', trigger: 'blur' },
        { min: 2, max: 10, message: '姓名长度为2-10个字符', trigger: 'blur' },
    ],
    businessImg: [{ required: true, message: '请上传营业执照', trigger: 'change' }],
})

watch(
    () => formData.businessImg,
    (newName) => {
        if (newName) {
            loadInfo(newName)
        }
    },
)

const loadInfo = async (url) => {
    try {
        const response = await extractBusinessImg(url)
        const { data } = response
        console.log('营业执照内容', data)
        // ElMessage.success('实名认证提交成功')

        formData.companyName = data['名称']
        formData.uscc = data['统一社会信用代码']
        formData.realName = data['法人']
        formData.businessContent = data['经营范围/业务范围']
        formData.registeredCapital = data['注册资本']
        formData.companyType = data['类型']
        formData.establishDate = data['成立日期']
    } catch (e) {
        console.log('发生了错误', e)
    }
}

// 同步更新表单字段的值
const UploadEmits = (data) => {
    imgList.value = data
    formData.businessImg = data.join(',')
}

// 提交表单
const handleSubmit = async () => {
    try {
        await formRef.value.validate()
        submitting.value = true

        const data = await userAuthSubmit(formData)
        console.log('data', data)

        ElMessage.success('企业认证提交成功')
        dialogVisible.value = false
    } catch (error) {
        console.log('表单验证失败', error)
    } finally {
        submitting.value = false
    }
}

defineExpose({
    openDialog,
})
</script>

<style scoped lang="scss">
.authentication-enterprise {
    padding: 0 20px 20px;
    box-sizing: border-box;

    .auth-notice {
        display: flex;
        align-items: center;
        background: #f0f7ff;
        border: 1px solid #d0e3ff;
        padding: 12px 16px;
        border-radius: 4px;
        margin-bottom: 24px;
        color: #333;
        font-size: 14px;

        i {
            color: #409eff;
            margin-right: 8px;
            font-size: 16px;
        }
    }

    .auth-form {
        .enterprise-form {
            width: 100%;
            max-width: 600px;
            margin: 0 auto;

            :deep(.el-form-item__label) {
                font-weight: 500;
                padding-bottom: 8px;
                color: #333;
            }

            .el-button {
                margin-top: 16px;
                height: 44px;
                font-size: 16px;
            }
        }
    }
}
</style>
