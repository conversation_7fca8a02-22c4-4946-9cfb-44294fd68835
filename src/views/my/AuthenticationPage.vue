<template>
    <div class="authentication-container">
        <div class="auth-header">
            <div class="header-message">
                <span class="warning-icon">!</span>
                <span>为了您的账号及资金安全，请尽早完成实名认证</span>
            </div>
            <div class="header-warning">
                <span class="warning-icon">!</span>
                <span>企业、机构用户请勿使用个人认证</span>
            </div>
        </div>

        <div class="auth-options">
            <AuthCard
                type="personal"
                :status="personStatus"
                title="个人认证"
                description="适用于个人用户，账号归属个人"
                @click="openDialog(1)"
            ></AuthCard>

            <AuthCard
                type="company"
                :status="companyStatus"
                title="企业认证"
                description="适用于企业、个体工商户、事业单位、学校等，账号归属于企业。"
                @click="openDialog(2)"
            ></AuthCard>
        </div>

        <!-- Dialog Components -->
        <auth-company ref="authCompanyRef"></auth-company>
        <auth-person ref="authPersonRef"></auth-person>
    </div>
</template>

<script setup>
import AuthCompany from './components/AuthCompany.vue'
import AuthPerson from './components/AuthPerson.vue'
import AuthCard from './components/AuthCard.vue'
import { getUserAuthList } from '@/api/authentication'

const authCompanyRef = ref(null)
const authPersonRef = ref(null)

const personStatus = ref(null)
const companyStatus = ref(null)

const authList = ref([])

onMounted(() => {
    loadAuthInfo()
})

const loadAuthInfo = async () => {
    try {
        const response = await getUserAuthList()
        console.log('认证信息', response)

        const { data } = response
        if (!data?.length) return

        authList.value = data

        // 状态 1已通过 3审核中 9无效
        const personInfo = data.find(item => item.type === 1)
        const companyInfo = data.find(item => item.type === 2)

        if (personInfo) {
            personStatus.value = personInfo.status
        }

        if (companyInfo) {
            personStatus.value = companyInfo.status
        }

        // 这里可以添加对 personInfo 和 companyInfo 的处理逻辑
    } catch (error) {
        console.error('获取认证信息失败:', error)
        // 可以添加错误处理逻辑，如显示错误提示
    }
}

const openDialog = (type) => {
    if (type === 1) {
        authPersonRef.value.openDialog(authList.value.find(n => n.type === 1))
    } else {
        authCompanyRef.value.openDialog(authList.value.find(n => n.type === 2))
    }
}

</script>

<style lang="scss" scoped>
.authentication-container {
    max-width: 900px;
    padding: 20px;

    .auth-header {
        background: #fffbe6;
        border: 1px solid #ffe58f;
        border-radius: 8px;
        padding: 12px 16px;
        margin-bottom: 24px;

        .header-message,
        .header-warning {
            display: flex;
            align-items: center;
            font-size: 14px;
            line-height: 1.5;
            color: #626066;

            &:not(:last-child) {
                margin-bottom: 8px;
            }
        }

        .warning-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 16px;
            height: 16px;
            background: #e6a23c;
            color: white;
            border-radius: 50%;
            font-size: 12px;
            margin-right: 8px;
            flex-shrink: 0;
        }
    }

    .auth-options {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 24px;
    }
}
</style>
