<template>
    <div class="address-list-view">
        <div class="address-list">
            <!-- 新增收货地址按钮 -->
            <div class="flex-row ai-center mb-20">
                <el-button
                    type="primary"
                    :disabled="addresses.length >= 25"
                    @click.stop="editAddress()"
                >
                    <svg-icon name="plus" color="#fff"></svg-icon>
                    <span>新增收货地址</span>
                </el-button>
                <div class="text-14 ml-12" style="color: #999">
                    您已创建{{ addresses.length }}个收货地址，最多可创建25个
                </div>
            </div>

            <!-- 地址列表 -->
            <div class="address-items">
                <div v-for="address in addresses" :key="address.id" class="address-item p-20 mb-16">
                    <!-- 地址标题 -->
                    <div class="flex-row ai-center jc-space-between mb-16">
                        <div class="flex-row ai-center">
                            <div class="text-16 mr-12" style="font-weight: 600; color: #333">
                                {{ address.realname }}
                            </div>
                            <div class="text-14 mr-12" style="color: #666">
                                {{ address.province }}
                            </div>
                            <el-tag v-if="address.status === 1" type="primary">默认地址</el-tag>
                        </div>
                        <div class="flex-row ai-center" @click="deleteAddr(address.id)">
                            <svg-icon name="close" color="#999"></svg-icon>
                        </div>
                    </div>

                    <!-- 收件人信息 -->
                    <div class="address-info">
                        <div class="flex-row mb-8">
                            <div class="text-14" style="color: #666; width: 80px; flex-shrink: 0">
                                收件人：
                            </div>
                            <div class="text-14" style="color: #333">{{ address.realname }}</div>
                        </div>

                        <div class="flex-row mb-8">
                            <div class="text-14" style="color: #666; width: 80px; flex-shrink: 0">
                                所在地区：
                            </div>
                            <div class="text-14" style="color: #333">
                                {{ address.province }} {{ address.city }} {{ address.area }}
                            </div>
                        </div>

                        <div class="flex-row mb-8">
                            <div class="text-14" style="color: #666; width: 80px; flex-shrink: 0">
                                地址：
                            </div>
                            <div class="text-14" style="color: #333">
                                {{ address.address }}
                            </div>
                        </div>

                        <div class="flex-row mb-8">
                            <div class="text-14" style="color: #666; width: 80px; flex-shrink: 0">
                                手机：
                            </div>
                            <div class="text-14" style="color: #333">{{ address.phone }}</div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex-row jc-flex-end mt-16">
                        <el-button
                            v-if="address.status !== 1"
                            type="primary"
                            text
                            size="small"
                            class="mr-8"
                            @click.stop="handleDefault(address)"
                        >
                            设为默认
                        </el-button>
                        <el-button type="primary" text size="small" @click="editAddress(address)">
                            编辑
                        </el-button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 地址表单对话框 -->
    <address-form ref="addressFormRef" @save="handleSaveAddress"></address-form>
</template>

<script setup>
import SvgIcon from '@/components/SvgIcon/SvgIcon.vue'
import { addAddress, deleteAddress, getAddressList } from '@/api/address.js'
import { useMessage } from '@/utils/useMessage.js'
import { ElMessageBox } from 'element-plus'
import AddressForm from '@/components/AddressDialog/AddressForm.vue'

// 地址表单
const addressFormRef = ref(null)

// 地址列表数据
const addresses = ref([])

const loadAddressList = async () => {
    try {
        // 写死不要分页
        const response = await getAddressList({ current: 1, size: 100 })
        console.log('地址列表', response)
        addresses.value = response.data['records'] || []
    } catch (error) {
        console.error('获取地址列表失败:', error)
    }
}

// 删除地址
const deleteAddr = (addressId) => {
    try {
        ElMessageBox.confirm('确定要删除这个地址吗？', '确认删除', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })
            .then(async () => {
                let params = {
                    ids: [addressId],
                }
                const response = await deleteAddress(params)
                console.log('删除地址结果', response)
                await loadAddressList()
                useMessage().success('删除成功')
            })
            .catch(() => {})
    } catch {
        // 用户取消删除
    }
}
// 编辑地址
const editAddress = (address) => {
    addressFormRef.value.openDialog(address)
}

// 设为默认地址
const handleDefault = async (params) => {
    ElMessageBox.confirm('确定把该地址设置为默认地址？', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(async () => {
            params.status = 1
            await addAddress(params)
            await loadAddressList()
        })
        .catch(() => {})
}

// 保存地址成功以后刷新列表
const handleSaveAddress = () => {
    loadAddressList()
}

onMounted(() => {
    loadAddressList()
})
</script>

<style lang="scss" scoped>
.address-list-view {
    .address-list {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;

        .address-item {
            background: white;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
        }
    }

    // 响应式设计
    @media (max-width: 768px) {
        .address-list {
            padding: 16px;

            .address-item {
                .flex-row {
                    &:first-child {
                        flex-wrap: wrap;
                        gap: 8px;
                    }
                }

                .address-info {
                    .flex-row {
                        flex-direction: column;
                        align-items: flex-start !important;

                        span:first-child {
                            width: auto !important;
                            margin-bottom: 4px;
                            font-weight: 500;
                        }
                    }
                }
            }
        }
    }
}
</style>
