<template>
    <div class="order-list-view">
        <div class="order-filter flex-row ai-center p-20">
            <el-radio-group v-model="filterType" @change="handleTypeChange">
                <el-radio-button :value="1">
                    <div class="flex-row ai-center">
                        <div class="_label">普通商品订单</div>
                    </div>
                </el-radio-button>
                <el-radio-button :value="3">
                    <div class="flex-row ai-center">
                        <div class="_label">拼团商品订单</div>
                    </div>
                </el-radio-button>
                <el-radio-button :value="5">
                    <div class="flex-row ai-center">
                        <div class="_label">服务订单</div>
                    </div>
                </el-radio-button>
                <el-radio-button :value="7">
                    <div class="flex-row ai-center">
                        <div class="_label">供需订单</div>
                    </div>
                </el-radio-button>
            </el-radio-group>
        </div>

        <!-- 订单状态筛选 -->
        <div class="order-filter flex-row ai-center p-20">
            <el-radio-group
                v-model="filterStatus"
                @change="handleFilterChange"
                v-if="filterType === 1 || filterType === 3"
            >
                <el-radio-button value="all">
                    <div class="flex-row ai-center">
                        <div class="_label">全部订单</div>
                    </div>
                </el-radio-button>
                <el-radio-button value="0">
                    <div class="flex-row ai-center">
                        <div class="_label">待付款</div>
                        <div class="_value" v-if="orderCount.waitPayCount > 0">
                            {{ orderCount.waitPayCount }}
                        </div>
                    </div>
                </el-radio-button>
                <el-radio-button value="1">
                    <div class="flex-row ai-center">
                        <div class="_label">待发货</div>
                        <div class="_value" v-if="orderCount.waitSendCount > 0">
                            {{ orderCount.waitSendCount }}
                        </div>
                    </div>
                </el-radio-button>
                <el-radio-button value="2">
                    <div class="flex-row ai-center">
                        <div class="_label">待收货</div>
                        <div class="_value" v-if="orderCount.waitReceiveCount > 0">
                            {{ orderCount.waitReceiveCount }}
                        </div>
                    </div>
                </el-radio-button>
                <el-radio-button value="3">
                    <div class="flex-row ai-center">
                        <div class="_label">已完成</div>
                    </div>
                </el-radio-button>
                <el-radio-button value="4">
                    <div class="flex-row ai-center">
                        <div class="_label">已取消</div>
                    </div>
                </el-radio-button>
            </el-radio-group>

            <el-radio-group
                v-model="filterStatus"
                @change="handleFilterChange"
                v-if="filterType === 5"
            >
                <el-radio-button value="all">
                    <div class="flex-row ai-center">
                        <div class="_label">全部订单</div>
                    </div>
                </el-radio-button>
                <el-radio-button value="0">
                    <div class="flex-row ai-center">
                        <div class="_label">待付款</div>
                        <div class="_value" v-if="orderCount.waitPayCount > 0">
                            {{ orderCount.waitPayCount }}
                        </div>
                    </div>
                </el-radio-button>
                <el-radio-button value="1">
                    <div class="flex-row ai-center">
                        <div class="_label">待接单</div>
                        <div class="_value" v-if="orderCount.waitSendCount > 0">
                            {{ orderCount.waitSendCount }}
                        </div>
                    </div>
                </el-radio-button>
                <el-radio-button value="6">
                    <div class="flex-row ai-center">
                        <div class="_label">服务中</div>
                        <div class="_value" v-if="orderCount.receiveCount > 0">
                            {{ orderCount.receiveCount }}
                        </div>
                    </div>
                </el-radio-button>
                <el-radio-button value="2">
                    <div class="flex-row ai-center">
                        <div class="_label">待确认</div>
                        <div class="_value" v-if="orderCount.waitReceiveCount > 0">
                            {{ orderCount.waitReceiveCount }}
                        </div>
                    </div>
                </el-radio-button>
                <el-radio-button value="3">
                    <div class="flex-row ai-center">
                        <div class="_label">已完成</div>
                    </div>
                </el-radio-button>
                <el-radio-button value="4">
                    <div class="flex-row ai-center">
                        <div class="_label">已取消</div>
                    </div>
                </el-radio-button>
            </el-radio-group>

            <el-radio-group
                v-model="filterStatus"
                @change="handleFilterChange"
                v-if="filterType === 7"
            >
                <el-radio-button value="all">
                    <div class="flex-row ai-center">
                        <div class="_label">全部订单</div>
                    </div>
                </el-radio-button>
                <el-radio-button value="0">
                    <div class="flex-row ai-center">
                        <div class="_label">待付款</div>
                        <div class="_value" v-if="orderCount.waitPayCount > 0">
                            {{ orderCount.waitPayCount }}
                        </div>
                    </div>
                </el-radio-button>
                <el-radio-button value="1">
                    <div class="flex-row ai-center">
                        <div class="_label">待发货</div>
                        <div class="_value" v-if="orderCount.waitSendCount > 0">
                            {{ orderCount.waitSendCount }}
                        </div>
                    </div>
                </el-radio-button>
                <el-radio-button value="2">
                    <div class="flex-row ai-center">
                        <div class="_label">已发货</div>
                    </div>
                </el-radio-button>
                <el-radio-button value="3">
                    <div class="flex-row ai-center">
                        <div class="_label">已完成</div>
                    </div>
                </el-radio-button>
            </el-radio-group>
        </div>

        <div v-loading="loading" class="order-list">
            <!-- 订单表格 -->
            <div class="order-table">
                <div v-for="order in orders" :key="order.id" class="order-group mb-20">
                    <!-- 订单头部 -->
                    <div
                        class="order-header flex-row ai-center jc-space-between pl-16 pr-16 pt-5 pb-5"
                    >
                        <div class="flex-row ai-center">
                            <div class="text-14" style="color: #999">{{ order.createTime }}</div>
                            <div class="text-14 ml-20" style="color: #999">
                                订单号：{{ order.orderNo }}
                            </div>

                            <div class="text-14 ml-20" style="color: #409eff">
                                {{ order.companyInfoVo.name }}
                            </div>
                        </div>
                        <svg-icon
                            class="delete-icon"
                            name="delete"
                            color="#999"
                            size="12"
                        ></svg-icon>
                    </div>

                    <!-- 商品表格 -->
                    <div class="products-table">
                        <!-- 表格头部 -->
                        <div class="table-header flex-row ai-center">
                            <div class="col-product text-14 p-12">{{ typeMap[order.type] }}</div>
                            <div class="col-quantity text-14 p-12 text-center">数量</div>
                            <div class="col-price text-14 p-12 text-center">单价</div>
                            <div class="col-status text-14 p-12 text-center">状态</div>
                            <div class="col-actions text-14 p-12 text-center">操作</div>
                        </div>

                        <!-- 商品行 -->
                        <div
                            v-for="(product, index) in order.detailList"
                            :key="index"
                            class="table-row flex-row ai-center"
                            style="border-bottom: 1px solid #f0f0f0"
                        >
                            <!-- 商品信息 -->
                            <div class="col-product flex-row ai-center p-12">
                                <div class="product-image mr-12">
                                    <img :src="product.goodsMainImg" :alt="product.goodsName" />
                                </div>
                                <div class="product-info">
                                    <div class="text-14" style="color: #333; line-height: 1.4">
                                        {{ product.goodsName }}
                                    </div>
                                </div>
                            </div>

                            <!-- 数量 -->
                            <div class="col-quantity text-center p-12">
                                <div class="text-14" style="color: #333">x{{ product.buyNum }}</div>
                            </div>

                            <!-- 价格 -->
                            <div class="col-price text-center p-12">
                                <div class="text-14" style="color: #333">
                                    ¥{{ product.buyPrice }}
                                </div>
                            </div>

                            <!-- 状态 -->
                            <div class="col-status text-center p-12">
                                <span class="text-12" :class="'order-status-' + product.status">
                                    {{ orderStatus[product.status] }}
                                </span>
                            </div>

                            <!-- 操作 -->
                            <div class="col-actions text-center p-12">
                                <div class="flex-column">
                                    <div class="_btn" @click="handleViewDetail(order, product)">
                                        查看详情
                                    </div>
                                    <template v-if="product.status === 0">
                                        <div class="_btn" @click="handlePayOrder(order, product)">
                                            支付订单
                                        </div>
                                    </template>
                                    <template v-if="product.status === 0 || product.status === 1">
                                        <div
                                            class="_btn"
                                            @click="handleCancelOrder(order, product)"
                                        >
                                            取消订单
                                        </div>
                                    </template>
                                    <template
                                        v-if="
                                            product.status === 1 ||
                                            product.status === 2 ||
                                            product.status === 3
                                        "
                                    >
                                        <div class="_btn">申请售后</div>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 空状态 -->
            <el-empty v-if="!loading && orders.length === 0" description="暂无订单数据"></el-empty>

            <!-- 加载更多提示 -->
            <load-more-tip
                :data-length="orders.length"
                :loading="isLoadingMore"
                :has-more="hasMoreData"
            />
        </div>

        <!-- 订单详情弹窗 -->
        <OrderDetail
            v-model="showOrderDetail"
            :order-id="selectedOrderId"
            @refresh="loadOrderList"
        />

        <!--  二维码弹窗  -->
        <qr-code-display
            ref="qrcodeRef"
            :pay-order-no="payOrderNo"
            @pay-over="handlePayOver"
        ></qr-code-display>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, onActivated, onDeactivated } from 'vue'
import SvgIcon from '@/components/SvgIcon/SvgIcon.vue'
import OrderDetail from './components/OrderDetail.vue'
import LoadMoreTip from '@/components/LoadMoreTip/LoadMoreTip.vue'
import {
    cancelOrder,
    countOrder,
    orderList,
    orderPay,
    servicePayOrder,
    supplyCancelOrder,
    supplyPayOrder,
} from '@/api/order.js'
import { orderStatus } from '@/utils/index.js'
import { useMessage } from '@/utils/useMessage.js'
import { ElMessageBox } from 'element-plus'
import QrCodeDisplay from '@/components/QrCodeDisplay/QrCodeDisplay.vue'
import { useInfiniteScroll } from '@/composables/useInfiniteScroll.js'

const filterStatus = ref('all')
const filterType = ref(1)
// 二维码弹窗
const qrcodeRef = ref()
const payOrderNo = ref('')

// 响应式数据
const loading = ref(false)
const orders = ref([])
const total = ref(0)

// 分页参数
const pagination = reactive({
    current: 1,
    size: 15, // 每页20条，适合下拉加载
})

// 下拉加载相关状态
const hasMoreData = ref(true)
const isLoadingMore = ref(false) // 防止重复加载的标志

const typeMap = {
    1: '普通商品',
    3: '拼团商品',
    5: '服务商品',
    7: '供需商品',
}

// 订单详情弹窗相关
const showOrderDetail = ref(false)
const selectedOrderId = ref(null)

const orderCount = ref({})

const handleTypeChange = (value) => {
    filterStatus.value = 'all'
    // 重置分页和状态
    pagination.current = 1
    hasMoreData.value = true
    // 重新获取数据（替换而不是追加）
    loadOrderList(false)
    loadOrderCount(value)
}

// 筛选处理
const handleFilterChange = (value) => {
    console.log('筛选状态:', value)
    // 重置分页和状态
    pagination.current = 1
    hasMoreData.value = true
    // 重新获取数据（替换而不是追加）
    loadOrderList(false)
}

// 重置数据
const resetData = () => {
    pagination.current = 1
    orders.value = []
    total.value = 0
    hasMoreData.value = true
}

// 加载更多数据的函数
const loadMoreData = async () => {
    // 防止重复加载
    if (!hasMoreData.value || isLoadingMore.value || loading.value) {
        return
    }

    try {
        isLoadingMore.value = true
        pagination.current += 1
        await loadOrderList(true)
    } finally {
        isLoadingMore.value = false
    }
}

// 设置无限滚动
const { loading: infiniteScrollLoading } = useInfiniteScroll({
    loadMore: loadMoreData,
    hasMore: hasMoreData,
    threshold: 100,
})

// 获取订单列表
const loadOrderList = async (isLoadMore = false) => {
    try {
        loading.value = true
        // 	0待支付>>1待发货>>2待收货>>3已完成>>4已取消>>5退款 6服务中
        // 构建查询参数
        const params = {
            current: pagination.current,
            size: pagination.size,
            typeList: [filterType.value],
            // 根据筛选状态添加参数
            ...(filterStatus.value !== 'all' && {
                statusList: [filterStatus.value],
            }),
        }

        console.log('请求参数:', params)

        // 调用接口
        const response = await orderList(params)

        if (response.code === 0) {
            const newData = response.data.records || []

            if (isLoadMore) {
                // 下拉加载：追加数据
                orders.value = [...orders.value, ...newData]
            } else {
                // 首次加载或筛选：替换数据
                orders.value = newData
            }

            total.value = response.data.total || 0

            // 只在首次加载时更新分页信息
            if (!isLoadMore) {
                pagination.current = response.data.current || 1
                pagination.size = response.data.size || 20
            }

            // 检查是否还有更多数据
            hasMoreData.value = orders.value.length < total.value
        } else {
            useMessage().error(response.msg || '获取订单列表失败')
            if (!isLoadMore) {
                orders.value = []
                total.value = 0
            }
        }
    } catch (error) {
        console.error('获取订单列表失败:', error)
        useMessage().error('获取订单列表失败')
        if (!isLoadMore) {
            orders.value = []
            total.value = 0
        }
    } finally {
        loading.value = false
    }
}

//  查看订单详情
const handleViewDetail = (order, item) => {
    selectedOrderId.value = order.id
    showOrderDetail.value = true
}

// 订单支付详情查询
const handlePayOver = (payInfo) => {
    console.log('payInfo', payInfo)
    resetData()
    loadOrderList()
}

//  支付订单
const handlePayOrder = async (order, item) => {
    console.log('order', order, item)
    payOrderNo.value = order.payOrderNo
    console.log('payOrderNo', payOrderNo.value)
    let params = {
        payOrderNo: order.payOrderNo,
        payMethod: 'wxPay',
        payMode: 2,
    }

    if (order.type === 7) {
        // 供需
        let payData = await supplyPayOrder(params)
        console.log('payData', payData)
        if (payData.code === 0) {
            qrcodeRef.value.openDialog(payData.data.message)
        }
    } else if (order.type === 5) {
        // 服务
        let payData = await servicePayOrder(params)
        console.log('payData', payData)
        if (payData.code === 0) {
            qrcodeRef.value.openDialog(payData.data.payInfo)
        }
    } else if (order.type === 3) {
        // 拼团商品
        let payData = await orderPay(params)
        console.log('payData', payData)
        if (payData.code === 0) {
            qrcodeRef.value.openDialog(payData.data.message)
        }
    } else {
        // 普通商品
        let payData = await orderPay(params)
        console.log('payData', payData)
        if (payData.code === 0) {
            qrcodeRef.value.openDialog(payData.data.message)
        }
    }

    await loadOrderCount(filterType.value)
}

// 取消订单
const handleCancelOrder = (order, item) => {
    console.log('order', order, item)
    ElMessageBox.confirm('确定要取消该订单吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '关闭',
        type: 'warning',
    })
        .then(async () => {
            if (order.type === 7) {
                let params = {
                    orderId: order.id,
                }

                const { code } = await supplyCancelOrder(params)
                console.log('取消订单结果', code)

                if (code === 0) {
                    useMessage().success('取消订单成功')
                    await loadOrderList()
                    await loadOrderCount(filterType.value)
                }
            } else {
                let params = {
                    orderId: order.id,
                }

                const { code } = await cancelOrder(params)
                console.log('取消订单结果', code)

                if (code === 0) {
                    useMessage().success('取消订单成功')
                    await loadOrderList()
                    await loadOrderCount(filterType.value)
                }
            }
        })
        .catch(() => {})
}

// 加载订单的数量汇总
const loadOrderCount = async (type) => {
    try {
        const response = await countOrder({ type })
        console.log('订单数量汇总', response)
        orderCount.value = response.data
    } catch (error) {
        console.error('获取订单数量汇总失败:', error)
    }
}

// 组件挂载时获取数据
onMounted(() => {
    loadOrderCount(1)
    loadOrderList()
})

// keep-alive 组件激活时的处理
onActivated(() => {
    // 可以在这里添加数据刷新逻辑，如果需要的话
    // 例如：检查数据是否过期，是否需要重新获取
})

// keep-alive 组件失活时的处理
onDeactivated(() => {
    // 重置加载状态，防止下次激活时状态异常
    isLoadingMore.value = false
})
</script>

<style scoped>
.order-list-view {
    .order-filter {
        margin: 0 20px 10px;
        background: white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);

        .flex-row {
            position: relative;

            ._value {
                position: absolute;
                top: -10px;
                right: -12px;
                background: red;
                color: white;
                font-size: 10px;
                width: 15px;
                height: 15px;
                border-radius: 50%;
                text-align: center;
                line-height: 15px;
            }
        }
    }

    .order-list {
        margin: 0 auto;
        padding: 20px;

        .order-group {
            background: white;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            overflow: hidden;

            .order-header {
                background: #ededed;
                border-bottom: 1px solid #e9ecef;

                .delete-icon {
                    &:hover {
                        color: #e74c3c !important;
                        cursor: pointer;
                    }
                }
            }

            .products-table {
                .table-header {
                    background: #f8f9fa;
                    border-bottom: 1px solid #e9ecef;

                    .text-14 {
                        color: #666;
                        font-weight: 500;
                    }
                }

                .table-row {
                    &:hover {
                        background: #f8f9fa;
                    }

                    &:last-child {
                        border-bottom: none;
                    }
                }

                .col-product {
                    flex: 1;
                    min-width: 300px;
                }

                .col-quantity {
                    width: 80px;
                    flex-shrink: 0;
                }

                .col-price {
                    width: 100px;
                    flex-shrink: 0;
                }

                .col-status {
                    width: 120px;
                    flex-shrink: 0;
                }

                .col-actions {
                    width: 120px;
                    flex-shrink: 0;

                    .flex-column {
                        gap: 5px;

                        ._btn {
                            font-size: 12px;
                            color: #626066;
                            cursor: pointer;

                            &:hover {
                                color: #409eff;
                                font-weight: 550;
                            }
                        }
                    }
                }

                .product-image {
                    width: 60px;
                    height: 60px;
                    border-radius: 4px;
                    overflow: hidden;
                    flex-shrink: 0;

                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }
                }

                .product-info {
                    min-width: 0;
                    flex: 1;
                }
            }
        }
    }

    @media (max-width: 768px) {
        .order-list {
            padding: 16px;

            .order-group {
                .order-header {
                    flex-direction: column;
                    align-items: flex-start !important;
                    gap: 8px;
                }

                .products-table {
                    .table-header {
                        display: none;
                    }

                    .table-row {
                        flex-direction: column;
                        align-items: stretch !important;
                        padding: 16px;

                        .col-product,
                        .col-quantity,
                        .col-price,
                        .col-status,
                        .col-actions {
                            width: 100%;
                            padding: 4px 0;
                        }

                        .col-product {
                            margin-bottom: 12px;
                        }
                    }
                }
            }
        }
    }
}
</style>
