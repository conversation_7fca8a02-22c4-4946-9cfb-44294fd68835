<template>
    <div class="error-page">
        <div class="error-content">
            <div class="error-code">404</div>
            <div class="error-title">页面未找到</div>
            <div class="error-description">
                抱歉，您访问的页面不存在或已被移除
            </div>
            <div class="error-actions">
                <el-button type="primary" @click="goHome">
                    <el-icon><House /></el-icon>
                    返回首页
                </el-button>
                <el-button @click="goBack">
                    <el-icon><ArrowLeft /></el-icon>
                    返回上页
                </el-button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { House, ArrowLeft } from '@element-plus/icons-vue'

const router = useRouter()

// 返回首页
const goHome = () => {
    router.push('/')
}

// 返回上一页
const goBack = () => {
    // 如果有历史记录则返回，否则跳转到首页
    if (window.history.length > 1) {
        router.go(-1)
    } else {
        router.push('/')
    }
}
</script>

<style scoped lang="scss">
.error-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
}

.error-content {
    text-align: center;
    background: white;
    padding: 60px 40px;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    max-width: 500px;
    width: 100%;
}

.error-code {
    font-size: 120px;
    font-weight: bold;
    color: #667eea;
    line-height: 1;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.error-title {
    font-size: 32px;
    font-weight: 600;
    color: #333;
    margin-bottom: 16px;
}

.error-description {
    font-size: 16px;
    color: #666;
    margin-bottom: 40px;
    line-height: 1.6;
}

.error-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

.error-actions .el-button {
    padding: 12px 24px;
    font-size: 16px;
    border-radius: 8px;
    min-width: 120px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .error-content {
        padding: 40px 20px;
        margin: 0 10px;
    }

    .error-code {
        font-size: 80px;
    }

    .error-title {
        font-size: 24px;
    }

    .error-description {
        font-size: 14px;
    }

    .error-actions {
        flex-direction: column;
        align-items: center;
    }

    .error-actions .el-button {
        width: 100%;
        max-width: 200px;
    }
}
</style>
