<template>
    <div class="supplier-page">
        <div class="enterprise-grid">
            <!-- 标题部分 -->
            <div class="section-header">
                <h2 class="section-title">渠道商</h2>
            </div>

            <!-- 企业网格 -->
            <div class="grid-container">
                <div
                    v-for="enterprise in tableList"
                    :key="enterprise.id"
                    class="enterprise-card"
                    @click="handleItemClick(enterprise)"
                >
                    <!-- 企业图标 -->
                    <div class="enterprise-icon">
                        <img
                            v-if="enterprise.logo"
                            :src="enterprise.logo"
                            :alt="enterprise.name"
                            class="icon-image"
                        />
                        <div
                            v-else
                            class="icon-placeholder"
                            :style="{ backgroundColor: enterprise.color }"
                        >
                            <span class="icon-text">{{ enterprise.shortName }}</span>
                        </div>
                    </div>

                    <!-- 企业名称 -->
                    <div class="enterprise-name">
                        {{ enterprise.name }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { supplierList } from '@/api/supplier.js'

// 默认企业数据（基于图片中的企业）
const tableList = ref([])

const loadList = async () => {
    const response = await supplierList()
    console.log('渠道商列表', response)
    tableList.value = response.data
}

// 处理企业点击事件
const handleItemClick = (enterprise) => {
    if (enterprise.url) {
        if (enterprise.url.startsWith('http')) {
            window.open(enterprise.url)
        } else {
            window.open('//' + enterprise.url)
        }
    }
}

onMounted(() => {
    loadList()
})
</script>

<style scoped lang="scss">
.supplier-page {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: calc(100vh - 60px);

    .enterprise-grid {
        width: 100%;
        padding: 24px;
        background-color: #fff;

        .section-header {
            margin-bottom: 24px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin: 0;
            padding-left: 4px;
        }

        .grid-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
            gap: 16px;
            margin: 0 auto;
        }

        .enterprise-card {
            border-radius: 12px;
            padding: 20px 16px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            border: 1px solid #e8eaed;

            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
                border-color: #dadce0;
            }

            .enterprise-icon {
                flex-shrink: 0;
                height: 40px;
                border-radius: 8px;
                overflow: hidden;

                .icon-image {
                    height: 100%;
                    object-fit: cover;
                }

                .icon-placeholder {
                    width: 100%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-weight: 600;
                    font-size: 14px;
                }
            }

            .enterprise-name {
                flex: 1;
                font-size: 15px;
                font-weight: 600;
                line-height: 1.4;
                color: #333;
                word-break: break-word;
            }
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .grid-container {
                grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
                gap: 14px;
            }
        }

        @media (max-width: 768px) {
            .enterprise-grid {
                padding: 16px;
            }

            .grid-container {
                grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
                gap: 12px;
            }

            .enterprise-card {
                padding: 16px 12px;
                gap: 10px;
            }

            .enterprise-icon {
                width: 36px;
                height: 36px;
            }

            .icon-placeholder {
                font-size: 12px;
            }

            .enterprise-name {
                font-size: 14px;
            }

            .section-title {
                font-size: 18px;
            }
        }

        @media (max-width: 480px) {
            .enterprise-grid {
                padding: 12px;
            }

            .grid-container {
                grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
                gap: 10px;
            }

            .enterprise-card {
                padding: 14px 10px;
                gap: 8px;
            }

            .enterprise-icon {
                width: 32px;
                height: 32px;
            }

            .icon-placeholder {
                font-size: 11px;
            }

            .enterprise-name {
                font-size: 13px;
            }
        }

        /* 超大屏幕优化 */
        @media (min-width: 1400px) {
            .grid-container {
                grid-template-columns: repeat(5, 1fr);
            }
        }
    }
}
</style>
