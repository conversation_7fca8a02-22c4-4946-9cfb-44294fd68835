<template>
    <div class="association-list" v-loading="loading">
        <div class="list-container">
            <association-item
                v-for="association in dataList"
                :key="association.id"
                :association="association"
                @item="handleItem"
            >
            </association-item>
        </div>
    </div>

    <!--  协会信息弹窗  -->
    <association-pop ref="associationPopRef"></association-pop>
</template>

<script setup>
import AssociationItem from '@/components/AssociationItem/AssociationItem.vue'
import { getAssociationList } from '@/api/association.js'
import { useMessage } from '@/utils/useMessage.js'
import AssociationPop from '@/views/supply/components/AssociationPop.vue'

// 响应式数据
const loading = ref(false)
const dataList = ref([])
const associationPopRef = ref()

// 获取协会列表
const fetchDataList = async () => {
    try {
        loading.value = true

        // 构建查询参数
        const params = {}

        // 尝试调用真实接口，如果失败则使用模拟数据
        let response
        try {
            response = await getAssociationList(params)
        } catch (error) {
            useMessage().error(error.msg || '获取协会列表失败')
        }

        if (response.code === 0) {
            dataList.value = response.data || []
        } else {
            useMessage().error(response.msg || '获取协会列表失败')
            dataList.value = []
        }
    } catch (error) {
        console.error('获取协会列表失败:', error)
        useMessage().error('获取协会列表失败')
        dataList.value = []
    } finally {
        loading.value = false
    }
}

const handleItem = (association) => {
    associationPopRef.value.openDialog(association.id)
}

// 组件挂载时获取数据
onMounted(() => {
    fetchDataList()
})
</script>

<style scoped>
.association-list {
    width: 100%;
    padding: 20px;
    background-color: #f5f5f5;
}

.list-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 1px;
    background-color: #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .association-item {
        padding: 16px;
    }

    .company-logo {
        width: 56px;
        height: 56px;
        font-size: 16px;
    }

    .company-logo-section {
        margin-right: 16px;
    }

    .action-section {
        margin-left: 16px;
    }
}

@media (max-width: 768px) {
    .association-list {
        padding: 12px;
    }

    .association-item {
        flex-direction: column;
        align-items: flex-start;
        padding: 16px;
        gap: 16px;
    }

    .company-logo-section {
        margin-right: 0;
        align-self: flex-start;
    }

    .company-info-section {
        width: 100%;
    }

    .action-section {
        margin-left: 0;
        flex-direction: row;
        width: 100%;
        justify-content: flex-end;
    }

    .action-btn {
        min-width: 70px;
        padding: 6px 12px;
        font-size: 13px;
    }

    .detail-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 2px;
    }

    .detail-separator {
        display: none;
    }
}

@media (max-width: 480px) {
    .company-name {
        font-size: 16px;
    }

    .company-logo {
        width: 48px;
        height: 48px;
        font-size: 14px;
    }

    .company-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .action-section {
        flex-direction: column;
        gap: 8px;
    }

    .action-btn {
        width: 100%;
    }
}
</style>
