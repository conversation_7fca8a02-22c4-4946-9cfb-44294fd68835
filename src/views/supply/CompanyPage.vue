<template>
    <div class="company-page">
        <!-- 搜索筛选区域 -->
        <div class="search-filter-section">
            <AiSearch :show-car="true" @search="handleAISearch" placeholder="请输入您的查询需求，AI自动为您匹配企业"/>
        </div>

        <!-- 企业列表 -->
        <div v-loading="loading" class="company-list-section">
            <div class="list-container">
                <company-item
                    v-for="company in dataList"
                    :key="company.id"
                    :company="company"
                    @item="handleItem"
                >
                </company-item>
            </div>

            <!-- 空状态 -->
            <el-empty
                v-if="!loading && dataList.length === 0"
                description="暂无企业数据"
            ></el-empty>

            <!-- 加载更多提示 -->
            <load-more-tip
                :data-length="dataList.length"
                :loading="isLoadingMore"
                :has-more="hasMoreData"
            />
        </div>
    </div>

    <!--  企业信息弹窗  -->
    <company-pop ref="companyPopRef"></company-pop>
</template>

<script setup>
import CompanyItem from '@/components/CompanyItem/CompanyItem.vue'
import LoadMoreTip from '@/components/LoadMoreTip/LoadMoreTip.vue'

import { companyInfoPage } from '@/api/company.js'
import { useMessage } from '@/utils/useMessage.js'
import { useInfiniteScroll } from '@/composables/useInfiniteScroll.js'
import CompanyPop from '@/views/supply/components/CompanyPop.vue'
import AiSearch from '@/components/AiSearch/AiSearch.vue'
import { aiSearch } from '@/api/main.js'

// 响应式数据
const loading = ref(false)
const dataList = ref([])
const total = ref(0)
const companyPopRef = ref()

// 搜索表单
const searchForm = reactive({
    title: '', // 企业名称
})

// 分页参数
const pagination = reactive({
    current: 1,
    size: 10, // 每页10条，适合下拉加载
})

// 当前查询是不是AI 查询
const isAIQuery = ref(false)
const searchName = ref('')

// 下拉加载相关状态
const hasMoreData = ref(true)
const isLoadingMore = ref(false) // 防止重复加载的标志

// 获取企业列表
const fetchDataList = async (isLoadMore = false) => {
    try {
        loading.value = true

        // 构建查询参数
        const params = {
            current: pagination.current,
            size: pagination.size,
            ...searchForm,
        }

        if (isAIQuery.value) {
            params.type = 4 // 	查询类型 1.产品 2.服务 3.供需 4.企业
            params.query = searchName.value
        }

        console.log('请求参数:', params)

        // 尝试调用真实接口
        let response
        try {
            if (isAIQuery.value) {
                response = await aiSearch(params)
                console.log('AI搜索结果', response)
            } else {
                response = await companyInfoPage(params)
            }
        } catch (error) {
            useMessage().error(error.msg || '获取企业列表失败')
            // 确保在错误时也重置加载状态
            loading.value = false
            return
        }

        if (response.code === 0) {
            const newData = response.data.records || []

            if (isLoadMore) {
                // 下拉加载：追加数据
                dataList.value = [...dataList.value, ...newData]
            } else {
                // 首次加载或搜索：替换数据
                dataList.value = newData
            }

            total.value = response.data.total || 0

            // 只在首次加载时更新分页信息
            if (!isLoadMore) {
                pagination.current = response.data.current || 1
                pagination.size = response.data.size || 20
            }

            // 检查是否还有更多数据
            hasMoreData.value = dataList.value.length < total.value
        } else {
            useMessage().error(response.msg || '获取企业列表失败')
            if (!isLoadMore) {
                dataList.value = []
                total.value = 0
            }
        }
    } catch (error) {
        console.error('获取企业列表失败:', error)
        useMessage().error('获取企业列表失败')
        if (!isLoadMore) {
            dataList.value = []
            total.value = 0
        }
    } finally {
        loading.value = false
    }
}

// 企业点击处理
const handleItem = (company) => {
    companyPopRef.value.openDialog(company.id)
}

// 搜索处理
const handleSearch = () => {
    // 重置分页和状态
    pagination.current = 1
    hasMoreData.value = true
    // 重新获取数据（替换而不是追加）
    fetchDataList(false)
}

// 重置数据
const resetData = () => {
    pagination.current = 1
    dataList.value = []
    total.value = 0
    hasMoreData.value = true
}

// 加载更多数据的函数
const loadMoreData = async () => {
    // 防止重复加载
    if (!hasMoreData.value || isLoadingMore.value || loading.value) {
        return
    }

    try {
        isLoadingMore.value = true
        pagination.current += 1
        await fetchDataList(true)
    } finally {
        isLoadingMore.value = false
    }
}

// 设置无限滚动
const { loading: infiniteScrollLoading } = useInfiniteScroll({
    loadMore: loadMoreData,
    hasMore: hasMoreData,
    threshold: 100,
})

const handleAISearch = async (keyWord) => {
    console.log('搜索关键词', keyWord)

    // 如果有 keyWord  ，则是AI查询，否则是普通查询
    isAIQuery.value = !!keyWord
    searchName.value = keyWord
    pagination.current = 1

    handleSearch()
}

// 组件挂载时获取数据
onMounted(() => {
    fetchDataList()
})

// keep-alive 组件激活时的处理
onActivated(() => {
    // 可以在这里添加数据刷新逻辑，如果需要的话
    // 例如：检查数据是否过期，是否需要重新获取
})

// keep-alive 组件失活时的处理
onDeactivated(() => {
    // 重置加载状态，防止下次激活时状态异常
    isLoadingMore.value = false
})
</script>

<style scoped lang="scss">
.company-page {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: calc(100vh - 60px);

    .search-filter-section {
        width: 1200px;
        margin: 0 auto 20px;
    }

    // 企业列表区域
    .company-list-section {
        margin-bottom: 20px;

        .list-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            gap: 1px;
            background-color: #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .company-page {
        padding: 10px;
    }
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .company-item {
        padding: 16px;
    }

    .company-logo {
        width: 56px;
        height: 56px;
        font-size: 16px;
    }

    .company-logo-section {
        margin-right: 16px;
    }

    .action-section {
        margin-left: 16px;
    }
}

@media (max-width: 768px) {
    .company-list-section {
        .list-container {
            padding: 12px;
        }
    }

    .company-item {
        flex-direction: column;
        align-items: flex-start;
        padding: 16px;
        gap: 16px;
    }

    .company-logo-section {
        margin-right: 0;
        align-self: flex-start;
    }

    .company-info-section {
        width: 100%;
    }

    .action-section {
        margin-left: 0;
        flex-direction: row;
        width: 100%;
        justify-content: flex-end;
    }

    .action-btn {
        min-width: 70px;
        padding: 6px 12px;
        font-size: 13px;
    }

    .detail-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 2px;
    }

    .detail-separator {
        display: none;
    }
}

@media (max-width: 480px) {
    .company-name {
        font-size: 16px;
    }

    .company-logo {
        width: 48px;
        height: 48px;
        font-size: 14px;
    }

    .company-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .action-section {
        flex-direction: column;
        gap: 8px;
    }

    .action-btn {
        width: 100%;
    }
}
</style>
