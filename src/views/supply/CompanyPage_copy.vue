<template>
    <div class="company-list" v-loading="loading">
        <div class="list-container">
            <company-item
                v-for="company in dataList"
                :key="company.id"
                :company="company"
                @item="handleItem"
            >
            </company-item>
        </div>
    </div>

    <!--  企业信息弹窗  -->
    <company-pop ref="companyPopRef"></company-pop>
</template>

<script setup>
import CompanyItem from '@/components/CompanyItem/CompanyItem.vue'

import { companyInfoPage } from '@/api/company.js'
import { useMessage } from '@/utils/useMessage.js'
import CompanyPop from '@/views/supply/components/CompanyPop.vue'

// 响应式数据
const loading = ref(false)
const dataList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(100)
const companyPopRef = ref()

// 搜索表单
const searchForm = reactive({
    title: '', // 商品名称
})

// 获取协会列表
const fetchDataList = async () => {
    try {
        loading.value = true

        // 构建查询参数
        const params = {
            ...searchForm,
            current: currentPage.value,
            size: pageSize.value,
        }

        // 尝试调用真实接口，如果失败则使用模拟数据
        let response
        try {
            response = await companyInfoPage(params)
        } catch (error) {
            useMessage().error(error.msg || '获取协会列表失败')
        }

        if (response.code === 0) {
            dataList.value = response.data.records || []
            total.value = response.data.total || 0
        } else {
            useMessage().error(response.msg || '获取协会列表失败')
            dataList.value = []
            total.value = 0
        }
    } catch (error) {
        console.error('获取协会列表失败:', error)
        useMessage().error('获取协会列表失败')
        dataList.value = []
        total.value = 0
    } finally {
        loading.value = false
    }
}

const handleItem = (company) => {
    companyPopRef.value.openDialog(company.id)
}

// 组件挂载时获取数据
onMounted(() => {
    fetchDataList()
})
</script>

<style scoped>
.company-list {
    width: 100%;
    padding: 20px;
    background-color: #f5f5f5;
}

.list-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 1px;
    background-color: #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .company-item {
        padding: 16px;
    }

    .company-logo {
        width: 56px;
        height: 56px;
        font-size: 16px;
    }

    .company-logo-section {
        margin-right: 16px;
    }

    .action-section {
        margin-left: 16px;
    }
}

@media (max-width: 768px) {
    .company-list {
        padding: 12px;
    }

    .company-item {
        flex-direction: column;
        align-items: flex-start;
        padding: 16px;
        gap: 16px;
    }

    .company-logo-section {
        margin-right: 0;
        align-self: flex-start;
    }

    .company-info-section {
        width: 100%;
    }

    .action-section {
        margin-left: 0;
        flex-direction: row;
        width: 100%;
        justify-content: flex-end;
    }

    .action-btn {
        min-width: 70px;
        padding: 6px 12px;
        font-size: 13px;
    }

    .detail-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 2px;
    }

    .detail-separator {
        display: none;
    }
}

@media (max-width: 480px) {
    .company-name {
        font-size: 16px;
    }

    .company-logo {
        width: 48px;
        height: 48px;
        font-size: 14px;
    }

    .company-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .action-section {
        flex-direction: column;
        gap: 8px;
    }

    .action-btn {
        width: 100%;
    }
}
</style>
