<template>
    <div v-loading="loading" class="procurement-page">
        <template v-for="(item,index) in dataList" :key="index">
            <div v-if="item.children.length > 0" class="enterprise-grid">
                <!-- 标题部分 -->
                <div class="section-header">
                    <h2 class="section-title">{{ item.name }}</h2>
                </div>

                <!-- 企业网格 -->
                <div class="grid-container">
                    <div
                        v-for="enterprise in item.children"
                        :key="enterprise.id"
                        class="enterprise-card flex-column jc-center ai-center"
                        @click="handleItemClick(enterprise)"
                    >
                        <!-- 企业图标 -->
                        <div class="enterprise-icon flex-row jc-center">
                            <img
                                v-if="enterprise.logo"
                                :src="enterprise.logo"
                                :alt="enterprise.name"
                                class="icon-image"
                            />
                        </div>

                        <!-- 企业名称 -->
                        <div class="enterprise-name">
                            {{ enterprise.name }}
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>
</template>

<script setup>
import { purchaseList } from '@/api/procurement.js'
import { useMessage } from '@/utils/useMessage.js'

// 响应式数据
const loading = ref(false)
const dataList = ref([])

// 加载数据
const loadList = async () => {
    try {
        loading.value = true

        // 构建查询参数
        const params = {}

        // 尝试调用真实接口，如果失败则使用模拟数据
        let response
        try {
            response = await purchaseList(params)
        } catch (error) {
            useMessage().error(error.msg || '获取数据失败')
        }

        if (response.code === 0) {
            let groupedData = enhancedGrouping(response.data) || []
            dataList.value = groupedData
        } else {
            useMessage().error(response.msg || '获取数据失败')
            dataList.value = []
        }
    } catch (error) {
        useMessage().error('获取数据失败')
        dataList.value = []
    } finally {
        loading.value = false
    }
}

// 根据type分组并统计数量排序
const enhancedGrouping = (data) => {
    const groups = {
        1: { name: '中央企业', count: 0, children: [], type: 1 },
        2: { name: '省属国企', count: 0, children: [], type: 2 },
        3: { name: '市属国企', count: 0, children: [], type: 3 },
        4: { name: '县属国企', count: 0, children: [], type: 4 }
    }

    data.forEach(item => {
        if (groups[item.type]) {
            groups[item.type].count++
            groups[item.type].children.push({
                id: item.id,
                name: item.name,
                url: item.url,
                delFlag: item.delFlag,
                logo: item.logo
            })
        }
    })

    // 转换为数组并按count排序
    return Object.values(groups).sort((a, b) => b.count - a.count)
}

// 处理企业点击事件
const handleItemClick = (enterprise) => {
    if (enterprise.url) {
        if (enterprise.url.startsWith('http')) {
            window.open(enterprise.url)
        } else {
            window.open('//' + enterprise.url)
        }
    }
}

onMounted(() => {
    loadList()
})
</script>

<style scoped lang="scss">
.procurement-page {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: calc(100vh - 60px);

    .enterprise-grid {
        width: 100%;
        padding: 24px;
        background-color: #FFF;
        margin-bottom: 20px;

        .section-header {
            margin-bottom: 24px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin: 0;
            padding-left: 4px;
        }

        .grid-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
            gap: 16px;
            margin: 0 auto;
        }

        .enterprise-card {
            border-radius: 12px;
            padding: 20px 16px;
            gap: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            border: 1px solid #e8eaed;

            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
                border-color: #dadce0;
            }

            .enterprise-icon {
                width: 100%;
                height: 40px;
                border-radius: 8px;
                overflow: hidden;

                .icon-image {
                    max-width: 100%;
                    max-height: 40px;
                    object-fit: contain;
                }
            }

            .enterprise-name {
                flex: 1;
                font-size: 15px;
                font-weight: 600;
                color: #333;
                line-height: 1.4;
                word-break: break-word;
            }
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .grid-container {
                grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
                gap: 14px;
            }
        }

        @media (max-width: 768px) {
            .enterprise-grid {
                padding: 16px;
            }

            .grid-container {
                grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
                gap: 12px;
            }

            .enterprise-card {
                padding: 16px 12px;
                gap: 10px;
            }

            .enterprise-icon {
                width: 36px;
                height: 36px;
            }

            .enterprise-name {
                font-size: 14px;
            }

            .section-title {
                font-size: 18px;
            }
        }

        @media (max-width: 480px) {
            .enterprise-grid {
                padding: 12px;
            }

            .grid-container {
                grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
                gap: 10px;
            }

            .enterprise-card {
                padding: 14px 10px;
                gap: 8px;
            }

            .enterprise-icon {
                width: 32px;
                height: 32px;
            }

            .enterprise-name {
                font-size: 13px;
            }
        }

        /* 超大屏幕优化 */
        @media (min-width: 1400px) {
            .grid-container {
                grid-template-columns: repeat(5, 1fr);
            }
        }
    }
}
</style>
