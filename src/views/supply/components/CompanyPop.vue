<template>
    <el-dialog
        v-model="dialogVisible"
        title="企业信息"
        width="1000px"
        top="5vh"
        :before-close="handleClose"
        class="company-info-dialog"
        :close-on-click-modal="false"
        :destroy-on-close="true"
    >
        <div v-if="companyInfo && companyInfo.id" class="company-dialog-content">
            <!-- 左侧：企业信息 -->
            <div class="left-panel">
                <el-scrollbar class="hide-scrollbar">
                    <!-- 企业基本信息 -->
                    <div class="company-header flex-row ai-flex-start p-20 mb-20">
                        <div class="company-logo" v-if="companyInfo.logo">
                            <img :src="companyInfo.logo" :alt="companyInfo.name" />
                        </div>
                        <div class="company-basic">
                            <h2 class="company-name text-20 mb-12 m-0">{{ companyInfo.name }}</h2>
                            <div class="company-details">
                                <div class="detail-item flex-row ai-center mb-8">
                                    <div class="_item flex-row ai-center">
                                        <div class="label text-14">法人：</div>
                                        <div class="value text-14">
                                            {{ companyInfo.legalPerson }}
                                        </div>
                                    </div>
                                    <div class="_item flex-row ai-center">
                                        <div class="label text-14">联系电话：</div>
                                        <div class="value text-14">
                                            {{ companyInfo.contactNumber || '未知' }}
                                        </div>
                                    </div>
                                </div>
                                <div class="detail-item flex-row ai-center jc-space-between mb-8">
                                    <div class="_item flex-row ai-center">
                                        <div class="label text-14">成立日期：</div>
                                        <div class="value text-14">
                                            {{ companyInfo.establishDate }}
                                        </div>
                                    </div>
                                    <div class="_item flex-row ai-center">
                                        <div class="label text-14">邮箱：</div>
                                        <div class="value text-14">
                                            {{ companyInfo.email || '无' }}
                                        </div>
                                    </div>
                                </div>
                                <div
                                    v-if="companyInfo.registeredCapital"
                                    class="detail-item flex-row ai-center mb-8"
                                >
                                    <div class="label text-14">注册资本：</div>
                                    <div class="value text-14">
                                        {{ companyInfo.registeredCapital }}
                                    </div>
                                </div>
                                <div class="detail-item flex-row ai-center mb-8">
                                    <div class="label text-14">企业类型：</div>
                                    <div class="value text-14">{{ companyInfo.companyType }}</div>
                                </div>
                                <div class="detail-item flex-row ai-center mb-8">
                                    <div class="label text-14">统一社会信用代码：</div>
                                    <div class="value text-12">{{ companyInfo.uscc }}</div>
                                </div>
                                <div class="detail-item address-item flex-row ai-flex-start">
                                    <div class="value text-14">
                                        {{ companyInfo.province }}{{ companyInfo.city
                                        }}{{ companyInfo.area }}{{ companyInfo.address }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="company-tabs">
                        <el-tabs v-model="activeTab" stretch @tab-click="handleClick">
                            <template v-for="tab in tabs" :key="tab.key">
                                <el-tab-pane :label="tab.label" :name="tab.key">
                                    <keep-alive>
                                        <component
                                            :is="tab.component"
                                            v-if="tab.key === activeTab"
                                            :company-info="companyInfo"
                                        ></component>
                                    </keep-alive>
                                </el-tab-pane>
                            </template>
                        </el-tabs>
                    </div>
                </el-scrollbar>
            </div>

            <!-- 右侧：聊天组件 -->
            <div class="right-panel">
                <ChatPanel
                    :seller-info="{
                        name: companyInfo.name || '企业客服',
                        avatar: companyInfo.logo || '/default-avatar.png',
                        isOnline: true,
                    }"
                />
            </div>
        </div>
    </el-dialog>
</template>

<script setup>
import ChatPanel from '@/components/ChatPanel/ChatPanel.vue'
import CompanyProducts from './company/CompanyProducts.vue'
import CompanyServices from './company/CompanyServices.vue'
import CompanySupply from './company/CompanySupply.vue'
import CompanyAssociations from './company/CompanyAssociations.vue'
import CompanyIntro from './company/CompanyIntro.vue'
import { companyDetail } from '@/api/company.js'

const dialogVisible = ref(false)
const activeTab = ref('products')
const companyInfo = ref(null)

// 标签页配置
const tabs = [
    { key: 'products', label: '企业产品', component: CompanyProducts },
    { key: 'services', label: '企业服务', component: CompanyServices },
    { key: 'supply', label: '企业供需', component: CompanySupply },
    { key: 'associations', label: '所属协会', component: CompanyAssociations },
    { key: 'intro', label: '企业简介', component: CompanyIntro },
]

// 打开弹窗
const openDialog = async (id) => {
    dialogVisible.value = true
    activeTab.value = 'products' // 默认显示产品页面
    loadDetail(id)
}

const loadDetail = (id) => {
    companyDetail({ id }).then((res) => {
        companyInfo.value = res.data

        console.log(' companyInfo.value ', companyInfo.value)
    })
}

// 关闭弹窗
const handleClose = () => {
    dialogVisible.value = false
}

const handleClick = () => {}

defineExpose({
    openDialog,
})
</script>

<style lang="scss">
.company-info-dialog {
    .company-dialog-content {
        display: flex;
        height: calc(80vh - 41px);
        min-height: 600px;
        gap: 20px;

        // 左侧面板
        .left-panel {
            flex: 1;
            display: flex;
            flex-direction: column;

            // 企业头部信息
            .company-header {
                background: #f8f9fa;
                border-radius: 8px;
                gap: 16px;

                .company-logo {
                    img {
                        width: 80px;
                        height: 80px;
                        object-fit: contain;
                        border-radius: 8px;
                        border: 1px solid #e8e8e8;
                    }
                }

                .company-basic {
                    flex: 1;

                    .company-name {
                        font-weight: 600;
                        color: #333;
                    }

                    .detail-item {
                        gap: 5px;

                        ._item {
                            flex: 1;
                        }

                        .label {
                            color: #666;
                            font-weight: 500;
                        }

                        .value {
                            flex: 1;
                            color: #333;
                        }
                    }
                }
            }

            .company-tabs {
                .el-tabs__header {
                    position: sticky;
                    top: 0;
                    z-index: 100;
                    background: white;
                }
            }
        }

        // 右侧面板
        .right-panel {
            width: 400px;
            flex-shrink: 0;
            border-left: 1px solid #e8e8e8;
            padding-left: 20px;
        }
    }
}

// 响应式设计
@media (max-width: 1024px) {
    .company-info-dialog {
        .company-dialog-content {
            flex-direction: column;
            height: auto;

            .left-panel {
                margin-bottom: 20px;
            }

            .right-panel {
                width: 100%;
                border-left: none;
                border-top: 1px solid #e8e8e8;
                padding-left: 0;
                padding-top: 20px;
            }
        }
    }
}
</style>
