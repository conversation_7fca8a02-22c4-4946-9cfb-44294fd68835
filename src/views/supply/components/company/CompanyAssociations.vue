<template>
    <div class="company-associations">
        <div class="associations-list">
            <join-association-item
                v-for="association in associations"
                :key="association.id"
                :association="association"
                @item="handleItem"
            ></join-association-item>
        </div>

        <!-- 空状态 -->
        <div v-if="!associations.length" class="empty-state text-center py-60 px-20">
            <div class="empty-icon text-48 mb-16">🏛️</div>
            <p class="empty-text text-16 m-0">暂无协会信息</p>
        </div>
    </div>

    <!--  打开协会的弹窗  -->
    <association-pop ref="associationPopRef"></association-pop>
</template>

<script setup>
import { useMessage } from '@/utils/useMessage.js'
import { queryByCompanyTenantId } from '@/api/association.js'
import JoinAssociationItem from '@/components/CompanyItem/JoinAssociationItem.vue'
import AssociationPop from '@/views/supply/components/AssociationPop.vue'

const props = defineProps({
    companyInfo: {
        type: Object,
        default: () => ({}),
    },
})

// 响应式数据
const associations = ref([])
const currentPage = ref(1)
const pageSize = ref(30)
const associationPopRef = ref()

const loadData = () => {
    let params = {
        companyTenantId: props.companyInfo?.tenantId,
        current: currentPage.value,
        size: pageSize.value,
    }
    queryByCompanyTenantId(params).then((res) => {
        console.log('企业所属协会', res)
        associations.value = res.data?.records || []
    })
}

const handleItem = (item) => {
    associationPopRef.value.openDialog(item.id)
}

// 初始化数据
onMounted(() => {
    nextTick(() => {
        loadData()
    })
})
</script>

<style scoped lang="scss">
.company-associations {
    height: 100%;
    overflow-y: auto;

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #999;

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .empty-text {
            font-size: 16px;
            margin: 0;
        }
    }
}
</style>
