<template>
    <div class="products-container">
        <div class="products-grid">
            <good-item
                v-for="item in products"
                :key="item.id"
                :item="item"
                @item-click="handleGoodsClick"
            >
            </good-item>
        </div>

        <!-- 空状态 -->
        <div v-if="!products.length" class="empty-state text-center py-60 px-20">
            <div class="empty-icon text-48 mb-16">📦</div>
            <p class="empty-text text-16">暂无产品信息</p>
        </div>
    </div>

    <!--  商品详情的弹窗  -->
    <good-detail-modal ref="goodModalRef" :product="selectedProduct"></good-detail-modal>
</template>

<script setup>
import { useMessage } from '@/utils/useMessage.js'
import { getGoodDetail, getGoodsPage } from '@/api/goods.js'
import GoodItem from '@/components/GoodItem/GoodItem.vue'
import GoodDetailModal from '@/views/trade/components/GoodDetailModal.vue'

const props = defineProps({
    companyInfo: {
        type: Object,
        default: () => ({}),
    },
})

const emit = defineEmits(['product-click', 'grab', 'inquiry'])

// 响应式数据
const products = ref([])
// 弹窗的一些设置
const selectedProduct = ref({})
const goodModalRef = ref()

const loadGoods = async () => {
    let params = {
        current: 1,
        size: 10,
        tenantId: props.companyInfo?.tenantId,
        queryObj: 2, // 查询对象:1协会 2企业
    }

    let response = await getGoodsPage(params)
    products.value = response.data.records || []
}

// 商品点击处理
const handleGoodsClick = (item) => {
    console.log('点击商品:', item)
    // 一定要用showModal，不然图片会显示异常，不会测试的话，永远不会复现，嘿嘿
    nextTick(async () => {
        const { data } = await getGoodDetail({ id: item.id })
        console.log('data', data)
        // 丰富商品数据
        selectedProduct.value = {
            ...item,
            ...data,
        }
        goodModalRef.value.openModal(selectedProduct)
    })
}

// 初始化数据
onMounted(() => {
    nextTick(() => {
        loadGoods()
    })
})
</script>

<style scoped lang="scss">
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 16px;
}

.empty-state {
    color: #999;

    .empty-text {
        margin: 0;
    }
}

// 响应式设计
@media (max-width: 768px) {
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: 12px;
    }
}
</style>
