<template>
    <div class="company-services">
        <div class="services-container">
            <div class="services-grid">
                <service-item
                    v-for="(service, index) in servicesList"
                    :key="index"
                    :service="service"
                    @item-click="handleServicesClick"
                >
                </service-item>
            </div>

            <!-- 空状态 -->
            <div v-if="!servicesList.length" class="empty-state text-center py-60 px-20">
                <div class="empty-icon text-48 mb-16">🛠️</div>
                <p class="empty-text text-16">暂无服务信息</p>
            </div>
        </div>
    </div>

    <!--  服务详情的弹窗  -->
    <service-detail-modal ref="serviceModalRef" :services="selectedService"></service-detail-modal>
</template>

<script setup>
import { listingsDetail, listingsList } from '@/api/service.js'
import ServiceItem from '@/components/ServiceItem/ServiceItem.vue'
import ServiceDetailModal from '@/views/trade/components/ServiceDetailModal.vue'

const props = defineProps({
    companyInfo: {
        type: Object,
        default: () => ({}),
    },
})

const emit = defineEmits(['service-click', 'contact', 'detail'])

// 响应式数据
const servicesList = ref([])
// 详情弹窗
const serviceModalRef = ref()
const selectedService = ref({})

const loadData = async () => {
    let params = {
        current: 1,
        size: 10,
        tenantId: props.companyInfo?.tenantId,
        queryObj: 2, // 查询对象:1协会 2企业
    }

    let response = await listingsList(params)
    servicesList.value = response.data.records || []
}

//  服务点击处理
const handleServicesClick = (item) => {
    console.log('点击服务:', item)
    // 一定要用showModal，不然图片会显示异常，不会测试的话，永远不会复现，嘿嘿
    nextTick(async () => {
        const { data } = await listingsDetail({ id: item.id })
        console.log('data', data)
        // 丰富商品数据
        selectedService.value = {
            ...item,
            ...data,
        }
        serviceModalRef.value.openModal(selectedService)
    })
}

// 初始化数据
onMounted(() => {
    nextTick(() => {
        loadData()
    })
})
</script>

<style scoped lang="scss">
.company-services {
    height: 100%;

    .services-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
        gap: 16px;
    }

    .empty-state {
        color: #999;

        .empty-text {
            margin: 0;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .services-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: 12px;
    }
}
</style>
