<template>
    <div class="association-intro">
        <!-- 空状态 -->
        <div class="empty-state text-center py-60 px-20">
            <div class="empty-icon text-48 mb-16">📄</div>
            <p class="empty-text text-16 m-0">暂无协会简介信息</p>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    associationInfo: {
        type: Object,
        default: () => ({}),
    },
})
</script>

<style scoped lang="scss">
.association-intro {
    height: 100%;
    overflow-y: auto;

    .empty-state {
        color: #999;
    }
}
</style>
