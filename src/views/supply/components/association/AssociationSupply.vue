<template>
    <div class="association-supply">
        <!-- 供需列表 -->
        <div class="supply-list">
            <supply-demand-item
                v-for="item in supplyList"
                :key="item.id"
                :item="item"
                @item-click="handleSupplyClick"
            ></supply-demand-item>
        </div>

        <!-- 空状态 -->
        <div v-if="!filteredSupplyList.length" class="empty-state text-center py-60 px-20">
            <div class="empty-icon text-48 mb-16">📋</div>
            <p class="empty-text text-16 m-0">暂无{{ getFilterText() }}信息</p>
        </div>
    </div>

    <!--  供需详情的弹窗  -->
    <supply-detail-modal ref="supplyDemandModalRef" :supplys="selectedSupply"></supply-detail-modal>
</template>

<script setup>
import { supplyDemandDetail, supplyDemandList } from '@/api/supply-demand.js'
import SupplyDemandItem from '@/components/SupplyDemandItem/SupplyDemandItem.vue'
import SupplyDetailModal from '@/views/trade/components/SupplyDetailModal.vue'

const props = defineProps({
    associationInfo: {
        type: Object,
        default: () => ({}),
    },
})

const emit = defineEmits(['supply-click', 'contact', 'detail'])

// 响应式数据
const supplyList = ref([])
const activeFilter = ref('all')

const supplyDemandModalRef = ref()
const selectedSupply = ref({})

// 筛选标签
const filterTabs = [
    { key: 'all', label: '全部' },
    { key: 'supply', label: '供应信息' },
    { key: 'demand', label: '需求信息' },
]

const loadData = async () => {
    let params = {
        current: 1,
        size: 10,
        tenantId: props.associationInfo?.tenantId,
        queryObj: 1, // 查询对象:1协会 2企业
    }

    let response = await supplyDemandList(params)
    supplyList.value = response.data.records || []
}

// 计算属性：过滤后的供需列表
const filteredSupplyList = computed(() => {
    if (activeFilter.value === 'all') {
        return supplyList.value
    }
    return supplyList.value.filter((item) => item.type === activeFilter.value)
})

// 获取筛选文本
const getFilterText = () => {
    const tab = filterTabs.find((t) => t.key === activeFilter.value)
    return tab ? tab.label : '供需'
}

const handleSupplyClick = (item) => {
    console.log('点击供需:', item)
    // 一定要用showModal，不然图片会显示异常，不会测试的话，永远不会复现，嘿嘿
    nextTick(async () => {
        const { data } = await supplyDemandDetail({ id: item.id })
        console.log('data', data)
        // 丰富商品数据
        selectedSupply.value = {
            ...item,
            ...data,
        }
        supplyDemandModalRef.value.openModal(selectedSupply)
    })
}

// 初始化数据
onMounted(() => {
    nextTick(() => {
        loadData()
    })
})
</script>

<style scoped lang="scss">
.association-supply {
    height: 100%;

    .supply-list {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
        gap: 16px;
    }

    .empty-state {
        color: #999;

        .empty-text {
            margin: 0;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .services-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: 12px;
    }
}
</style>
