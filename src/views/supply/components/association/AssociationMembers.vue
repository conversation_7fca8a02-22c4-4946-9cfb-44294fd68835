<template>
    <div class="association-members">
        <!-- 会员列表 -->
        <div class="members-list">
            <!--  复制CompanyItem.vue略改  -->
            <members-item
                v-for="company in membersList"
                :key="company.id"
                :company="company"
                @item="handleItem"
            >
            </members-item>
        </div>

        <!-- 空状态 -->
        <div v-if="!membersList.length" class="empty-state text-center py-60 px-20">
            <div class="empty-icon text-48 mb-16">👥</div>
            <p class="empty-text text-16 m-0">暂无{{ getFilterText() }}会员信息</p>
        </div>
    </div>

    <!--  企业信息弹窗  -->
    <company-pop ref="companyPopRef"></company-pop>
</template>

<script setup>
import { useMessage } from '@/utils/useMessage.js'
import { associationMember } from '@/api/association.js'
import CompanyPop from '@/views/supply/components/CompanyPop.vue'
import MembersItem from '@/components/AssociationItem/MembersItem.vue'

const props = defineProps({
    associationInfo: {
        type: Object,
        default: () => ({}),
    },
})

const emit = defineEmits(['member-click', 'contact', 'detail'])

// 响应式数据
const membersList = ref([])
const activeFilter = ref('all')

const companyPopRef = ref()

// 筛选标签
const filterTabs = [
    { key: 'all', label: '全部会员' },
    { key: 'director', label: '理事单位' },
    { key: 'vice', label: '副会长单位' },
    { key: 'ordinary', label: '普通会员' },
]

// 获取筛选文本
const getFilterText = () => {
    const tab = filterTabs.find((t) => t.key === activeFilter.value)
    return tab ? tab.label.replace('会员', '') : ''
}

const handleItem = (company) => {
    companyPopRef.value.openDialog(company.companyId)
}

const loadData = async () => {
    let params = {
        current: 1,
        size: 100,
        tenantId: props.associationInfo?.tenantId,
    }

    let response = await associationMember(params)
    membersList.value = response.data.records || []
}

// 初始化数据
onMounted(() => {
    nextTick(() => {
        loadData()
    })
})
</script>

<style scoped lang="scss">
.association-members {
    height: 100%;
    overflow: hidden;

    .empty-state {
        color: #999;
    }
}
</style>
