<template>
    <el-dialog
        v-model="dialogVisible"
        title="协会信息"
        width="1000px"
        top="5vh"
        :before-close="handleClose"
        class="association-info-dialog"
        :close-on-click-modal="false"
        :destroy-on-close="true"
    >
        <div v-if="associationInfo && associationInfo.id" class="association-dialog-content">
            <!-- 左侧：企业信息 -->
            <div class="left-panel">
                <el-scrollbar class="hide-scrollbar">
                    <!-- 企业基本信息 -->
                    <div class="association-header flex-row ai-flex-start p-20 mb-20">
                        <div class="association-logo" v-if="associationInfo.logo">
                            <img :src="associationInfo.logo" :alt="associationInfo.name" />
                        </div>
                        <div class="association-basic">
                            <h2 class="association-name text-20 mb-12 m-0">
                                {{ associationInfo.name }}
                            </h2>
                            <div class="association-details">
                                <div class="detail-item flex-row ai-center mb-8">
                                    <div class="_item flex-row ai-center">
                                        <div class="label text-14">法人：</div>
                                        <div class="value text-14">
                                            {{ associationInfo.legalPerson }}
                                        </div>
                                    </div>
                                    <div class="_item flex-row ai-center">
                                        <div class="label text-14">联系电话：</div>
                                        <div class="value text-14">
                                            {{ associationInfo.contactNumber || '未知' }}
                                        </div>
                                    </div>
                                </div>
                                <div class="detail-item flex-row ai-center jc-space-between mb-8">
                                    <div class="_item flex-row ai-center">
                                        <div class="label text-14">成立日期：</div>
                                        <div class="value text-14">
                                            {{ associationInfo.establishDate }}
                                        </div>
                                    </div>
                                    <div class="_item flex-row ai-center">
                                        <div class="label text-14">邮箱：</div>
                                        <div class="value text-14">
                                            {{ associationInfo.email || '无' }}
                                        </div>
                                    </div>
                                </div>
                                <div
                                    v-if="associationInfo.registeredCapital"
                                    class="detail-item flex-row ai-center mb-8"
                                >
                                    <div class="label text-14">注册资本：</div>
                                    <div class="value text-14">
                                        {{ associationInfo.registeredCapital }}
                                    </div>
                                </div>
                                <div class="detail-item flex-row ai-center mb-8">
                                    <div class="label text-14">统一社会信用代码：</div>
                                    <div class="value text-12">{{ associationInfo.uscc }}</div>
                                </div>
                                <div class="detail-item address-item flex-row ai-flex-start">
                                    <div class="value text-14">
                                        {{ associationInfo.province }}{{ associationInfo.city
                                        }}{{ associationInfo.area }}{{ associationInfo.address }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="association-tabs">
                        <el-tabs v-model="activeTab" stretch @tab-click="handleClick">
                            <template v-for="tab in tabs" :key="tab.key">
                                <el-tab-pane :label="tab.label" :name="tab.key">
                                    <keep-alive>
                                        <component
                                            :is="tab.component"
                                            v-if="tab.key === activeTab"
                                            :association-info="associationInfo"
                                        ></component>
                                    </keep-alive>
                                </el-tab-pane>
                            </template>
                        </el-tabs>
                    </div>
                </el-scrollbar>
            </div>

            <!-- 右侧：聊天组件 -->
            <div class="right-panel">
                <ChatPanel
                    :seller-info="{
                        name: associationInfo.name || '企业客服',
                        avatar: associationInfo.logo || '/default-avatar.png',
                        isOnline: true,
                    }"
                />
            </div>
        </div>
    </el-dialog>
</template>

<script setup>
import ChatPanel from '@/components/ChatPanel/ChatPanel.vue'
import AssociationProducts from './association/AssociationProducts.vue'
import AssociationServices from './association/AssociationServices.vue'
import AssociationSupply from './association/AssociationSupply.vue'
import AssociationMembers from './association/AssociationMembers.vue'
import AssociationIntro from './association/AssociationIntro.vue'
import { getAssociation } from '@/api/association.js'

const dialogVisible = ref(false)
const activeTab = ref('products')
const associationInfo = ref(null)

// 标签页配置
const tabs = [
    { key: 'products', label: '协会产品', component: AssociationProducts },
    { key: 'services', label: '协会服务', component: AssociationServices },
    { key: 'supply', label: '协会供需', component: AssociationSupply },
    { key: 'members', label: '协会会员', component: AssociationMembers },
    { key: 'intro', label: '协会简介', component: AssociationIntro },
]

// 打开弹窗
const openDialog = async (id) => {
    dialogVisible.value = true
    activeTab.value = 'products' // 默认显示产品页面
    loadDetail(id)
}

const loadDetail = (id) => {
    getAssociation({ id }).then((res) => {
        associationInfo.value = res.data

        console.log(' associationInfo.value ', associationInfo.value)
    })
}

// 关闭弹窗
const handleClose = () => {
    dialogVisible.value = false
}

const handleClick = () => {}

defineExpose({
    openDialog,
})
</script>

<style lang="scss">
.association-info-dialog {
    .association-dialog-content {
        display: flex;
        height: calc(80vh - 41px);
        min-height: 600px;
        gap: 20px;

        // 左侧面板
        .left-panel {
            flex: 1;
            display: flex;
            flex-direction: column;

            // 企业头部信息
            .association-header {
                background: #f8f9fa;
                border-radius: 8px;
                gap: 16px;

                .association-logo {
                    img {
                        width: 80px;
                        height: 80px;
                        object-fit: contain;
                        border-radius: 8px;
                        border: 1px solid #e8e8e8;
                    }
                }

                .association-basic {
                    flex: 1;

                    .association-name {
                        font-weight: 600;
                        color: #333;
                    }

                    .detail-item {
                        gap: 5px;

                        ._item {
                            flex: 1;
                        }

                        .label {
                            color: #666;
                            font-weight: 500;
                        }

                        .value {
                            flex: 1;
                            color: #333;
                        }
                    }
                }
            }

            .association-tabs {
                .el-tabs__header {
                    position: sticky;
                    top: 0;
                    z-index: 100;
                    background: white;
                }
            }
        }

        // 右侧面板
        .right-panel {
            width: 400px;
            flex-shrink: 0;
            border-left: 1px solid #e8e8e8;
            padding-left: 20px;
        }
    }
}

// 响应式设计
@media (max-width: 1024px) {
    .association-info-dialog {
        .association-dialog-content {
            flex-direction: column;
            height: auto;

            .left-panel {
                margin-bottom: 20px;
            }

            .right-panel {
                width: 100%;
                border-left: none;
                border-top: 1px solid #e8e8e8;
                padding-left: 0;
                padding-top: 20px;
            }
        }
    }
}
</style>
