import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user.js'
import { ElMessage } from 'element-plus'
import Layout from '@/layout/index.vue'

const router = createRouter({
    history: createWebHistory(import.meta.env.BASE_URL),
    routes: [
        {
            path: '/404',
            name: 'error',
            component: () => import('@/views/error/404Page.vue'),
        },
        {
            path: '/',
            redirect: '/trade',
        },
        {
            path: '/trade',
            component: Layout,
            meta: { title: '服贸对接', icon: 'dashboard' },
            children: [
                {
                    path: '',
                    redirect: { name: 'GoodPage' }, // ✅ 使用命名路由跳转
                },
                {
                    path: 'good',
                    component: () => import('@/views/trade/GoodPage.vue'),
                    name: 'GoodPage',
                    meta: { title: '找产品', icon: 'zhaochanpin', keepAlive: true },
                },
                {
                    path: 'service',
                    component: () => import('@/views/trade/ServicePage.vue'),
                    name: 'ServicePage',
                    meta: { title: '找服务', icon: 'zhaofuwu', keepAlive: true },
                },
                {
                    path: 'supply-demand',
                    component: () => import('@/views/trade/SupplyDemand.vue'),
                    name: 'SupplyDemand',
                    meta: { title: '供需对接', icon: 'gongxuyuce' , keepAlive: true},
                },
            ],
        },
        {
            path: '/supply',
            component: Layout,
            meta: { title: '资源对接', icon: 'dashboard' },
            children: [
                {
                    path: '',
                    redirect: { name: 'Association' }, // ✅ 使用命名路由跳转
                },
                {
                    path: 'association',
                    component: () => import('@/views/supply/AssociationPage.vue'),
                    name: 'Association',
                    meta: { title: '商协会', icon: 'xiehuihuiyuan' , keepAlive: true},
                },
                {
                    path: 'company',
                    component: () => import('@/views/supply/CompanyPage.vue'),
                    name: 'Company',
                    meta: { title: '企业目录', icon: 'qiyexinxi' , keepAlive: true},
                },
                {
                    path: 'procurement',
                    component: () => import('@/views/supply/PurchaserPage.vue'),
                    name: 'Procurement',
                    meta: { title: '采购人(央国企)', icon: 'caigouren' , keepAlive: true},
                },
                {
                    path: 'supplier',
                    component: () => import('@/views/supply/SupplierPage.vue'),
                    name: 'Supplier',
                    meta: { title: '渠道商', icon: 'qudaoshang', keepAlive: true },
                },
            ],
        },
        {
            path: '/mine',
            component: Layout,
            meta: { title: '我的', icon: 'dashboard' },
            children: [
                {
                    path: '',
                    redirect: { name: 'UserCenter' }, // ✅ 使用命名路由跳转
                },
                {
                    path: 'user-center',
                    component: () => import('@/views/my/UserCenter.vue'),
                    name: 'UserCenter',
                    meta: { title: '个人中心', icon: 'user', requiresAuth: true, keepAlive: false },
                },
                {
                    path: 'order',
                    component: () => import('@/views/my/OrderPage.vue'),
                    name: 'Order',
                    meta: { title: '订单管理', icon: 'dingdanguanli', requiresAuth: true , keepAlive: false},
                },
                {
                    path: 'address',
                    component: () => import('@/views/my/AddressPage.vue'),
                    name: 'AddressPage',
                    meta: { title: '地址管理', icon: 'location', requiresAuth: true, keepAlive: false },
                },
                // {
                //     path: 'after-sale',
                //     component: () => import('@/views/my/SaleAfter.vue'),
                //     name: 'AfterSale',
                //     meta: { title: '售后服务', icon: 'shouhoufuwu', requiresAuth: true, keepAlive: false },
                // },
                {
                    path: 'authentication',
                    component: () => import('@/views/my/AuthenticationPage.vue'),
                    name: 'Authentication',
                    meta: { title: '实名认证', icon: 'shimingrenzheng', requiresAuth: true, keepAlive: false },
                },
                // {
                //     path: 'invoice',
                //     component: () => import('@/views/my/InvoicePage.vue'),
                //     name: 'Invoice',
                //     meta: { title: '发票管理', icon: 'fapiaoguanli', requiresAuth: true , keepAlive: false},
                // },
            ],
        },
        // 捕获所有未匹配的路由，重定向到404页面
        {
            path: '/:pathMatch(.*)*',
            name: 'NotFound',
            redirect: '/404',
        },
    ],
})

// 全局路由守卫
router.beforeEach((to, from, next) => {
    // 检查路由是否需要登录
    if (to.meta['requiresAuth']) {
        const userStore = useUserStore()

        // 检查用户是否已登录
        if (!userStore.isLoggedIn) {
            // 未登录，显示提示并触发登录弹窗
            // ElMessage.warning('请先登录后再访问该页面')

            // 触发登录弹窗
            // 这里需要通过事件总线或者全局状态来触发登录弹窗
            window.dispatchEvent(
                new CustomEvent('show-login-dialog', {
                    detail: {
                        redirectPath: to.fullPath, // 登录成功后跳转的路径
                    },
                }),
            )

            // 阻止路由跳转，停留在当前页面
            next(false)
            return
        }
    }

    // 允许路由跳转
    next()
})

export default router
