import request from '@/utils/request'

// 服务分类列表
export function listingsCate(params) {
    return request({
        url: '/mix/listingsCate/queryTree',
        method: 'get',
        params: params,
    })
}

// 查询已上架分类列表
export function queryUpList(params) {
    return request({
        url: '/mix/listingsCate/queryUpList',
        method: 'get',
        params: params,
    })
}

// 服务列表分页查询
export function listingsList(params) {
    return request({
        url: '/mix/listings/queryPage',
        method: 'get',
        params: params,
    })
}

// 服务详情
export function listingsDetail(params) {
    return request({
        url: '/mix/listings/queryById',
        method: 'get',
        params: params,
    })
}
