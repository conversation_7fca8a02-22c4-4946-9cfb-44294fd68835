import request from '@/utils/request'

// 企业列表 - 分页查询
export function companyInfoPage(params) {
    return request({
        url: '/mix/companyInfo/queryPage',
        method: 'get',
        params: params
    })
}

// 企业列表 - 查询全部
export function companyInfoList(params) {
    return request({
        url: '/mix/companyInfo/queryList',
        method: 'get',
        params: params
    })
}

// 查询企业详情
export function companyDetail(params) {
    return request({
        url: '/mix/companyInfo/queryById',
        method: 'get',
        params: params
    })
}
