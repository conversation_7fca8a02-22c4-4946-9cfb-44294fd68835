import request from '@/utils/request'

// 查询地址列表
export function getAddressList(params) {
    return request({
        url: '/mix/userAddress/queryPage',
        method: 'post',
        data: params
    })
}

// 删除地址
export function deleteAddress(params) {
    return request({
        url: '/mix/userAddress/delete',
        method: 'post',
        data: params
    })
}

// 新增地址
export function addAddress(params) {
    return request({
        url: '/mix/userAddress/add',
        method: 'post',
        data: params
    })
}

// 更新地址
export function updateAddress(params) {
    return request({
        url: '/mix/userAddress/update',
        method: 'post',
        data: params
    })
}

// 获取默认地址
export function getDefaultAddress(params) {
    return request({
        url: '/mix/userAddress/address/getDefaultAddress',
        method: 'get',
        params: params
    })
}
