import request from '@/utils/request'

export function getUserAuthList(params) {
    return request({
        url: '/mix/userAuth/getNormalList',
        method: 'get',
        params: params
    })
}

export function userAuthSubmit(params) {
    return request({
        url: '/mix/userAuth/submit',
        method: 'post',
        data: params
    })
}

export function extractBusinessImg(url) {
    // /dify/extractBusinessImg
    return request({
        url: '/admin/dify/extractBusinessImg',
        method: 'post',
        data: {
            businessImg: url
        }
    })
}
