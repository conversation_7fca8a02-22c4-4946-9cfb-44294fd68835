import request from '@/utils/request'

// 查询分类树
export function supplyDemandCate(params) {
    return request({
        url: '/mix/supplyDemandCate/queryTree',
        method: 'get',
        params: params
    })
}

// 查询已上架分类列表
export function supplyCateUpList(params) {
    return request({
        url: '/mix/supplyDemandCate/queryUpList',
        method: 'get',
        params: params
    })
}

// 供需列表分页查询
export function supplyDemandList(params) {
    return request({
        url: '/mix/supplyDemand/queryPage',
        method: 'get',
        params: params
    })
}

// /supplyDemand/add
export function supplyDemandAdd(params) {
    return request({
        url: '/mix/supplyDemand/add',
        method: 'post',
        data: params
    })
}

// 供需详情
export function supplyDemandDetail(params) {
    return request({
        url: '/mix/supplyDemand/queryById',
        method: 'get',
        params: params
    })
}
