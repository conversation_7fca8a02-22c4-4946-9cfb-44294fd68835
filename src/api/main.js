import request from '@/utils/request'

export function getSmsCode(params) {
    return request({
        url: '/mix/sms/send/21',
        method: 'get',
        params: params,
    })
}

// 短信登录
export const loginBySms = (data) => {
    const grant_type = 'sms'
    const scope = 'server'
    const basicAuth = 'Basic bWl4Om1peA=='
    const loginSocialSource = 'serviceTradeWeb'
    const loginSource = 5

    return request({
        url: '/auth/oauth2/token',
        headers: {
            Authorization: basicAuth,
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        method: 'post',
        data: { ...data, grant_type, scope, loginSource, loginSocialSource },
    })
}

// 省市区
export function getAreaCity(params) {
    return request({
        url: '/mix/areaCity/area/tree',
        method: 'get',
        params: params,
    })
}

// 省市区树状
export function getAreaCityByCode(params) {
    return request({
        url: '/mix/areaCity/city/detailByAdcode',
        method: 'get',
        params: params,
    })
}

//  AI查询
export function aiSearch(params) {
    return request({
        url: '/mix/tIndexConfig/aiQuery',
        method: 'get',
        params: params,
    })
}

// AI对话
export function aiChat(params) {
    return request({
        url: '/ai/chat/msg',
        method: 'post',
        data: params,
    })
}
