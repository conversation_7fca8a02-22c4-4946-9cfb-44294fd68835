import request from '@/utils/request'

// 协会列表(没有分页)
export function getAssociationList(params) {
    return request({
        url: '/mix/associationInfo/queryList',
        method: 'get',
        params: params,
    })
}

// 协会列表(有分页)
export function getAssociationPage(params) {
    return request({
        url: '/mix/associationInfo/queryPage',
        method: 'get',
        params: params,
    })
}

// 获取协会详情
export function getAssociation(params) {
    return request({
        url: '/mix/associationInfo/queryById',
        method: 'get',
        params: params,
    })
}

// 根据协会租户ID查询协会会员
export function associationMember(params) {
    return request({
        url: '/mix/associationMember/queryPageByAssociation',
        method: 'get',
        params: params,
        headers: {
            'tenant-id': params?.tenantId,
        },
    })
}

// 根据企业的租户ID查询加入的协会
export function queryByCompanyTenantId(params) {
    return request({
        url: '/mix/associationInfo/queryByCompanyTenantId',
        method: 'get',
        params: params,
        headers: {
            'companyenantId': params?.companyTenantId,
        },
    })
}
