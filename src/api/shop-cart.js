import request from '@/utils/request'
// http://112.84.178.28:9101/doc.html#/woagent-mix/%E8%B4%AD%E7%89%A9%E8%BD%A6/addCarNum

// 清空购物车
export function clearCart(params) {
    return request({
        url: '/mix/car/clear',
        method: 'post',
        data: params
    })
}

// 删除购物车里面的某一个商品
export function deleteCart(params) {
    return request({
        url: '/mix/car/delete',
        method: 'post',
        data: params
    })
}

// 将商品加入购物车
export function addCart(params) {
    return request({
        url: '/mix/car/addCar',
        method: 'post',
        data: params
    })
}

// 购物车加减数量
export function addCartNum(params) {
    return request({
        url: '/mix/car/addCarNum',
        method: 'post',
        data: params
    })
}

// 购物车列表查询
export function cartList(params) {
    return request({
        url: '/mix/car/getPage',
        method: 'get',
        params: params
    })
}

// 统计购物车宝贝数量
export function cartCount(params) {
    return request({
        url: '/mix/car/count',
        method: 'get',
        params: params
    })
}
