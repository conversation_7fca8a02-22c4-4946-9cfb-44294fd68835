import request from '@/utils/request.js'

/*
 *  这个是查询订单的支付状态
 * */
export function getPayStatus(params) {
    return request({
        url: '/mix/payOrder/getPayOrder',
        method: 'post',
        data: params,
        headers: {
            needToken: true,
        },
    })
}

// 支付详情（和getPayStatus是一个功能）
export function getPayDetail(params) {
    return request({
        url: '/mix/order/payDetail',
        method: 'get',
        params: params,
        headers: {
            needToken: true,
        },
    })
}

// 校验是否可以参团
export function isJoinGroup(params) {
    return request({
        url: '/mix/order/validateGroup',
        method: 'get',
        params: params,
        headers: {
            needToken: true,
        },
    })
}

// 取消订单
export function cancelOrder(params) {
    return request({
        url: '/mix/order/cancel',
        method: 'post',
        data: params,
        headers: {
            needToken: true,
        },
    })
}

// 确认收货
export function confirmOrder(params) {
    return request({
        url: '/mix/order/confirm',
        method: 'post',
        data: params,
        headers: {
            needToken: true,
        },
    })
}

// 物流详情
export function getFreightList(params) {
    return request({
        url: '/mix/order/getFreightList',
        method: 'get',
        params: params,
        headers: {
            needToken: true,
        },
    })
}

// 订单数量汇总
export function countOrder(params) {
    return request({
        url: '/mix/order/countOrder',
        method: 'get',
        params: params,
        headers: {
            needToken: true,
        },
    })
}

/*
 *  这个是商品的相关订单接口
 * */

// 确认订单查询
export function orderPlace(params) {
    return request({
        url: '/mix/order/place',
        method: 'post',
        data: params,
        headers: {
            needToken: true,
        },
    })
}

// 添加订单
export function addOrder(params) {
    return request({
        url: '/mix/order/add',
        method: 'post',
        data: params,
        headers: {
            needToken: true,
        },
    })
}

// 支付订单
export function orderPay(params) {
    return request({
        url: '/mix/order/pay',
        method: 'post',
        data: params,
        headers: {
            needToken: true,
        },
    })
}

// 订单列表
export function orderList(params) {
    return request({
        url: '/mix/order/getList',
        method: 'get',
        params: params,
        headers: {
            needToken: true,
        },
    })
}

// 订单详情
export function orderDetail(params) {
    return request({
        url: '/mix/order/order/get',
        method: 'get',
        params: params,
        headers: {
            needToken: true,
        },
    })
}

/*
 *  这个是服务的相关订单接口
 * */

// 服务创建订单
export function serviceCreateOrder(params) {
    return request({
        url: '/mix/listings/createOrder',
        method: 'post',
        data: params,
        headers: {
            needToken: true,
        },
    })
}

// 服务支付订单
export function servicePayOrder(params) {
    return request({
        url: '/mix/listings/payOrder',
        method: 'post',
        data: params,
        headers: {
            needToken: true,
        },
    })
}

/*
 *  这个是供需的相关订单接口
 * */

// 供需下单
export function supplyCreateOrder(params) {
    return request({
        url: '/mix/supplyDemand/placeOrder',
        method: 'post',
        data: params,
        headers: {
            needToken: true,
        },
    })
}

// 供需支付订单
export function supplyPayOrder(params) {
    return request({
        url: '/mix/supplyDemand/payOrder',
        method: 'post',
        data: params,
        headers: {
            needToken: true,
        },
    })
}

// 供需取消订单
export function supplyCancelOrder(params) {
    return request({
        url: '/mix/supplyDemand/cancelOrder',
        method: 'post',
        data: params,
        headers: {
            needToken: true,
        },
    })
}
