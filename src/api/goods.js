import request from '@/utils/request.js'

// 加载商品分类列表
export function goodsCateList(params) {
    return request({
        url: '/mix/goodsCate/getList',
        method: 'get',
        params: params
    })
}

// 商品列表分页查询
export function getGoodsPage(params) {
    return request({
        url: '/mix/goods/queryPage',
        method: 'get',
        params: params
    })
}

// 商品详情
export function getGoodDetail(params) {
    return request({
        url: '/mix/goods/queryById',
        method: 'get',
        params: params
    })
}
