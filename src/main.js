import 'virtual:svg-icons-register'

import './styles/main.scss'
import 'element-plus/es/components/message/style/css' // 全局导入 ElMessage 样式
import 'element-plus/es/components/message-box/style/css' // 全局导入 ElMessageBox 样式

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

const pinia = createPinia()
pinia.use(piniaPluginPersistedstate) // 启用持久化插件

import App from './App.vue'
import router from './router'

const app = createApp(App)

app.use(pinia)
app.use(router)

app.mount('#app')
