import { computed } from 'vue'
import { useUserStore } from '@/stores/user.js'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

/**
 * 认证相关的组合式函数
 */
export function useAuth() {
    const userStore = useUserStore()
    const router = useRouter()

    // 计算属性
    const isLoggedIn = computed(() => userStore.isLoggedIn)
    const userInfo = computed(() => userStore.userInfo)
    const userName = computed(() => userStore.userName)
    const userAvatar = computed(() => userStore.userAvatar)

    /**
     * 要求用户登录
     * @param {string} redirectPath - 登录成功后跳转的路径
     * @param {string} message - 提示消息
     * @returns {boolean} 是否已登录
     */
    const requireLogin = (redirectPath = '', message = '请先登录后再进行此操作') => {
        if (isLoggedIn.value) {
            return true
        }

        // 显示提示消息
        ElMessage.warning(message)

        // 触发登录弹窗
        window.dispatchEvent(
            new CustomEvent('show-login-dialog', {
                detail: {
                    redirectPath: redirectPath || router.currentRoute.value.fullPath,
                },
            }),
        )

        return false
    }

    /**
     * 跳转到需要登录的页面
     * @param {string} path - 目标路径
     * @param {string} message - 未登录时的提示消息
     */
    const navigateWithAuth = (path, message = '请先登录后再访问该页面') => {
        if (isLoggedIn.value) {
            router.push(path)
        } else {
            requireLogin(path, message)
        }
    }

    /**
     * 执行需要登录的操作
     * @param {Function} callback - 需要执行的回调函数
     * @param {string} message - 未登录时的提示消息
     * @returns {boolean} 是否执行了回调
     */
    const executeWithAuth = (callback, message = '请先登录后再进行此操作') => {
        if (isLoggedIn.value) {
            callback()
            return true
        } else {
            requireLogin('', message)
            return false
        }
    }

    /**
     * 执行需要登录的操作（支持异步回调）
     * @param {Function} callback - 需要执行的回调函数
     * @param {string} message - 未登录时的提示消息
     * @returns {Promise<boolean>} 是否执行了回调
     */
    const executeWithAuthAsync = (callback, message) => {
        return new Promise((resolve) => {
            if (isLoggedIn.value) {
                callback()
                resolve(true)
            } else {
                // 显示提示消息
                if (message) {
                    ElMessage.warning(message)
                }

                // 触发登录弹窗（不传 redirectPath）
                window.dispatchEvent(
                    new CustomEvent('show-login-dialog', {
                        detail: { redirectPath: '' },
                    }),
                )

                // 监听登录成功事件
                const handleLoginSuccess = () => {
                    window.removeEventListener('login-success', handleLoginSuccess)
                    callback()
                    resolve(true)
                }

                window.addEventListener('login-success', handleLoginSuccess)
                resolve(false)
            }
        })
    }

    /**
     * 登出用户
     */
    const logout = () => {
        userStore.logout()

        // 跳转到首页
        router.push('/')

        ElMessage.success('已退出登录')
    }

    /**
     * 检查是否有特定权限
     * @param {string} permission - 权限标识
     * @returns {boolean} 是否有权限
     */
    const hasPermission = (permission) => {
        if (!isLoggedIn.value) {
            return false
        }

        // 这里可以根据实际的权限系统来实现
        if (userInfo.value && userInfo.value.permissions) {
            return userInfo.value.permissions.includes(permission)
        }

        // 默认已登录用户有基本权限
        return true
    }

    return {
        // 状态
        isLoggedIn,
        userInfo,
        userName,
        userAvatar,

        // 方法
        requireLogin,
        navigateWithAuth,
        executeWithAuth,
        executeWithAuthAsync,
        logout,
        hasPermission,
    }
}
