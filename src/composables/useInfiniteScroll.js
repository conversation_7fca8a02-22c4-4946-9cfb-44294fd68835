// src/composables/useInfiniteScroll.js
import { ref, onMounted, onUnmounted } from 'vue'

/**
 * 无限滚动 composable
 * 直接监听 layout 中的滚动容器，实现下拉加载功能
 */
export function useInfiniteScroll(options = {}) {
    const {
        loadMore,           // 加载更多数据的函数
        hasMore = ref(true), // 是否还有更多数据
        threshold = 100,    // 距离底部多少像素时触发加载
        delay = 1000       // 延迟设置监听器的时间（确保 DOM 已渲染）
    } = options

    const loading = ref(false)
    let cleanup = null
    let isThrottled = false // 节流标志

    // 查找滚动容器
    const findScrollContainer = () => {
        return document.querySelector('.main-scrollbar .el-scrollbar__wrap')
    }

    // 滚动事件处理
    const handleScroll = async () => {
        const scrollContainer = findScrollContainer()
        if (!scrollContainer) return

        const scrollTop = scrollContainer.scrollTop
        const scrollHeight = scrollContainer.scrollHeight
        const clientHeight = scrollContainer.clientHeight
        const distanceToBottom = scrollHeight - scrollTop - clientHeight

        // 距离底部小于阈值时触发加载
        if (distanceToBottom <= threshold && hasMore.value && !loading.value && !isThrottled) {
            try {
                loading.value = true
                isThrottled = true // 设置节流标志

                await loadMore()

                // 加载完成后，延迟一段时间再允许下次触发
                setTimeout(() => {
                    isThrottled = false
                }, 1000) // 1秒内不允许重复触发

            } catch (error) {
                console.error('Load more error:', error)
                isThrottled = false // 出错时也要重置节流标志
            } finally {
                loading.value = false
            }
        }
    }

    // 设置滚动监听
    const setupListener = () => {
        const scrollContainer = findScrollContainer()
        if (scrollContainer) {
            scrollContainer.addEventListener('scroll', handleScroll)
            return () => scrollContainer.removeEventListener('scroll', handleScroll)
        }
        return null
    }

    // 开始监听
    const startListening = () => {
        setTimeout(() => {
            cleanup = setupListener()
        }, delay)
    }

    // 停止监听
    const stopListening = () => {
        if (cleanup) {
            cleanup()
            cleanup = null
        }
    }

    // 重置状态
    const reset = () => {
        hasMore.value = true
        loading.value = false
    }

    // 自动开始监听
    onMounted(() => {
        startListening()
    })

    // 组件卸载时清理
    onUnmounted(() => {
        stopListening()
    })

    return {
        loading,
        hasMore,
        startListening,
        stopListening,
        reset
    }
}
