@use 'base';
@use 'common';

input {
    outline: none;
    border: none;
    background: none;
}


img, video {
    max-width: 100%;
}

img {
    vertical-align: middle;
}

/* 始终隐藏滚动条样式，但是还要保持滚动效果 */
.hide-scrollbar {
    > .el-scrollbar__bar {
        > .el-scrollbar__thumb {
            display: none;
        }
    }
}

.el-drawer__header {
    margin-bottom: 0 !important;
    padding-bottom: 20px !important;
    background: #f4f5f9;
}

.el-cascader, .el-select, .el-input-number {
    width: 100%;
}

/* 订单支付状态 */
.order-status-0 { /* 待支付 - 醒目的珊瑚红 */
    color: #ff4d4f;
    background-color: #fff1f0;
    border: 1px solid #ffccc7;
    padding: 2px 8px;
    border-radius: 4px;
}

.order-status-1 { /* 待发货 - 温暖的琥珀色 */
    color: #fa8c16;
    background-color: #fff7e6;
    border: 1px solid #ffe7ba;
    padding: 2px 8px;
    border-radius: 4px;
}

.order-status-2 { /* 待收货 - 信任感蓝色 */
    color: #1890ff;
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;
    padding: 2px 8px;
    border-radius: 4px;
}

.order-status-3 { /* 已完成 - 生态绿 */
    color: #52c41a;
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
    padding: 2px 8px;
    border-radius: 4px;
}

.order-status-4 { /* 已取消 - 中性灰 */
    color: #8c8c8c;
    background-color: #fafafa;
    border: 1px solid #d9d9d9;
    padding: 2px 8px;
    border-radius: 4px;
}

.order-status-5 { /* 退款 - 深红色 */
    color: #cf1322;
    background-color: #fff1f0;
    border: 1px solid #ffa39e;
    padding: 2px 8px;
    border-radius: 4px;
}

.order-status-6 { /* 服务中 - 专业紫 */
    color: #722ed1;
    background-color: #f9f0ff;
    border: 1px solid #d3adf7;
    padding: 2px 8px;
    border-radius: 4px;
}
