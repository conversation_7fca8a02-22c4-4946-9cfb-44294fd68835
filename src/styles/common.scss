/* 基础重置和盒模型设置 */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

/* Flex 布局类 */
/* flex布局 */
@each $direction in column, row {
    .flex-#{$direction} {
        display: flex;
        flex-direction: #{$direction};
        flex-wrap: wrap;
    }
}

/* flex布局 -- 对齐方式 */
@each $justifycontent in center, space-between, flex-start, flex-end {
    .jc-#{$justifycontent} {
        justify-content: #{$justifycontent};
    }
}

/* flex布局 -- 内容对齐方式 */
@each $alignitems in flex-start, flex-end, center, baseline, stretch {
    .ai-#{$alignitems} {
        align-items: #{$alignitems};
    }
}

/* 溢出省略号 */
.text-over {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

@each $clamp in 2, 3, 4, 5 {
    .text-over#{$clamp} {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: #{$clamp};
        -webkit-box-orient: vertical;
    }
}

@each $clamp in left, center, right {
    .text-#{$clamp} {
        text-align: #{$clamp};
    }
}

@for $i from 8 through 60 {
    /* 字体大小类 */
    .text-#{$i} {
        font-size: #{$i}px;
    }

    /* 内边距生成 */
    .p-#{$i} {
        padding: #{$i}px;
    }

    .pt-#{$i} {
        padding-top: #{$i}px;
    }

    .pr-#{$i} {
        padding-right: #{$i}px;
    }

    .pb-#{$i} {
        padding-bottom: #{$i}px;
    }

    .pl-#{$i} {
        padding-left: #{$i}px;
    }

    .px-#{$i} {
        padding-left: #{$i}px;
        padding-right: #{$i}px;
    }

    .py-#{$i} {
        padding-top: #{$i}px;
        padding-bottom: #{$i}px;
    }

    /* 外边距生成 */
    .m-#{$i} {
        margin: #{$i}px;
    }

    .mt-#{$i} {
        margin-top: #{$i}px;
    }

    .mr-#{$i} {
        margin-right: #{$i}px;
    }

    .mb-#{$i} {
        margin-bottom: #{$i}px;
    }

    .ml-#{$i} {
        margin-left: #{$i}px;
    }

    .mx-#{$i} {
        margin-left: #{$i}px;
        margin-right: #{$i}px;
    }

    .my-#{$i} {
        margin-top: #{$i}px;
        margin-bottom: #{$i}px;
    }
}

/* 溢出省略号 */
.text-over {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

@each $clamp in 2, 3, 4, 5 {
    .text-over#{$clamp} {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: #{$clamp};
        -webkit-box-orient: vertical;
    }
}
