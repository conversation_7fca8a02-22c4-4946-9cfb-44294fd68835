<template>
    <div v-if="dataLength > 0" class="load-more-section">
        <div v-if="loading" class="loading-tip">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>{{ loadingText }}</span>
        </div>
        <div v-else-if="!hasMore" class="no-more-tip">
            {{ noMoreText }}
        </div>
        <div v-else class="scroll-tip">
            {{ scrollText }}
        </div>
    </div>
</template>

<script setup>
import { Loading } from '@element-plus/icons-vue'

// 定义 props
defineProps({
    // 数据数组长度
    dataLength: {
        type: Number,
        required: true,
        default: 0
    },
    // 是否正在加载
    loading: {
        type: Boolean,
        default: false
    },
    // 是否还有更多数据
    hasMore: {
        type: Boolean,
        default: true
    },
    // 自定义文本
    loadingText: {
        type: String,
        default: '正在加载更多...'
    },
    noMoreText: {
        type: String,
        default: '没有更多数据了'
    },
    scrollText: {
        type: String,
        default: '继续滚动加载更多...'
    }
})
</script>

<style scoped lang="scss">
.load-more-section {
    padding: 20px;
    text-align: center;
    
    .loading-tip {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        color: #666;
        font-size: 14px;
        
        .el-icon {
            font-size: 16px;
        }
    }
    
    .no-more-tip, .scroll-tip {
        color: #999;
        font-size: 14px;
    }
    
    .scroll-tip {
        color: #666;
    }
}
</style>
