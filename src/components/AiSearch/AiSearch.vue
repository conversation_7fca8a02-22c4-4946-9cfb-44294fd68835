<template>
    <div class="ai-search flex-row">
        <div class="_item search-box">
            <input
                v-model="searchInput"
                class="search-input"
                :placeholder="placeholder"
                @keydown.enter.prevent="handleSearch"
            />
            <div class="search-info flex-row jc-flex-end ai-center" @click.stop="handleSearch">
                <svg-icon name="xiangji" size="20" color="#999"></svg-icon>
                <div class="_text">AI搜索</div>
            </div>
        </div>

        <!--        <div class="_item shop-car flex-row jc-center ai-center" v-if="showCar">-->
        <!--            <svg-icon name="caigouren"></svg-icon>-->
        <!--            <div class="shop-btn">购物车</div>-->
        <!--        </div>-->
    </div>
</template>
<script setup>
import SvgIcon from '@/components/SvgIcon/SvgIcon.vue'

defineProps({
    placeholder: {
        type: String,
        default: '请输入您的查询需求，AI自动为您匹配产品',
    },
    showCar: {
        type: Boolean,
        default: false,
    },
})

const emit = defineEmits(['search'])
const searchInput = ref(null)

const handleSearch = () => {
    emit('search', searchInput.value)
}
</script>

<style scoped lang="scss">
.ai-search {
    gap: 20px;
    position: sticky;
    top: 0; /* 触发粘性定位的位置 */
    z-index: 1000; /* 确保在其他内容之上 */
    background: #fff; /* 背景色防止内容透出 */
    border-radius: 15px;

    ._item {
        &.search-box {
            flex-grow: 1;
            border: 2px solid #409eff;
            border-radius: 15px;
            padding: 0 12px;
            position: relative;

            .search-input {
                outline: none;
                border: none;
                width: 100%;
                height: 50px;
            }

            .search-info {
                width: 140px;
                height: 50px;
                position: absolute;
                right: 3px;
                top: 0;
                gap: 15px;

                .svg-icon {
                    cursor: pointer;
                }

                ._text {
                    width: 80px;
                    height: 45px;
                    background: #409eff;
                    color: #fff;
                    font-size: 16px;
                    font-weight: 500;
                    border-radius: 10px;
                    text-align: center;
                    line-height: 45px;
                    cursor: pointer;
                }
            }
        }

        &.shop-car {
            width: 200px;
            border-radius: 10px;
            font-size: 18px;
            font-weight: 500;
            background: linear-gradient(to right, #ffca00, #ff9402);
            color: #ffffff;
            letter-spacing: 2px;
            cursor: pointer;
            gap: 5px;
        }
    }
}
</style>
