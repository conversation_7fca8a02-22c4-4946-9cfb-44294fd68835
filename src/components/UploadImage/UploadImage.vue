<template>
    <div class="upload-box">
        <template v-if="props.imageUrls.length">
            <el-upload
                v-for="(url, index) in props.imageUrls"
                :key="index"
                action="#"
                :id="generateUuid()"
                style="margin-right: 10px;margin-bottom: 10px;"
                :class="['upload', self_disabled ? 'disabled' : '', drag ? 'no-border' : '']"
                :multiple="true"
                :disabled="self_disabled"
                :show-file-list="false"
                :http-request="handleHttpUpload"
                :before-upload="beforeUpload"
                :on-success="uploadSuccess"
                :on-error="uploadError"
                :drag="drag"
                :accept="fileType.join(',')">
                <div class="upload-images">
                    <div class="upload-image-wrapper">
                        <img :src="url.includes('http') ? url :  url" class="upload-image" />
                        <div class="upload-handle" @click.stop>
                            <div class="handle-icon"
                                 @click="imgViewVisible = true; currentImgIndex = index;">
                                <el-icon :size="props.iconSize">
                                    <ZoomIn />
                                </el-icon>
                                <span v-if="!props.iconSize">预览</span>
                            </div>
                            <div class="handle-icon" @click="deleteImg(index)"
                                 v-if="!self_disabled">
                                <el-icon :size="props.iconSize">
                                    <Delete />
                                </el-icon>
                                <span v-if="!props.iconSize">删除</span>
                            </div>
                        </div>
                    </div>
                </div>
            </el-upload>
            <div class="el-upload__tip">
                <slot name="tip"></slot>
            </div>
        </template>
        <template v-if="props.imageUrls.length < maxSize">
            <el-upload
                action="#"
                :id="generateUuid()"
                :class="['upload', self_disabled ? 'disabled' : '', drag ? 'no-border' : '']"
                :multiple="true"
                :disabled="self_disabled"
                :show-file-list="false"
                :http-request="handleHttpUpload"
                :before-upload="beforeUpload"
                :on-success="uploadSuccess"
                :on-error="uploadError"
                :drag="drag"
                :accept="fileType.join(',')"
            >
                <div class="upload-empty">
                    <slot name="empty">
                        <el-icon>
                            <Plus />
                        </el-icon>
                    </slot>
                </div>
            </el-upload>
            <div class="el-upload__tip">
                <slot name="tip"></slot>
            </div>
        </template>
        <el-image-viewer
            :teleported="true"
            v-if="imgViewVisible"
            @close="imgViewVisible = false"
            :url-list="props.imageUrls.map(url => url.includes('http') ? url : url)"
            :initial-index="currentImgIndex"
        />
    </div>
</template>
<script setup>
import { ref, computed, inject } from 'vue'
import { ElNotification, formContextKey, formItemContextKey } from 'element-plus'
import { generateUUID } from '@/utils/index'
import request from '@/utils/request'
import { Delete, Plus, ZoomIn } from '@element-plus/icons-vue'

// 接受父组件参数
const props = defineProps({
    imageUrls: {
        type: Array,
        default: () => [] // 必须提供默认空数组
    },
    uploadFileUrl: {
        type: String,
        // default: '/admin/file/upload'
        default: '/mix/file/inner/upload'
    },
    drag: {
        type: Boolean,
        default: true
    },
    disabled: {
        type: Boolean,
        default: false
    },
    fileSize: {
        type: Number,
        default: 5
    },
    fileType: {
        type: Array,
        default: () => ['image/jpeg', 'image/png', 'image/gif']
    },
    height: {
        type: String,
        default: '150px'
    },
    width: {
        type: String,
        default: '150px'
    },
    maxSize: {
        type: Number,
        default: 1
    },
    borderRadius: {
        type: String,
        default: '8px'
    },
    dir: {
        type: String,
        default: ''
    }
})

// 生成组件唯一id
const generateUuid = () => {
    return 'id-' + generateUUID()
}

// 查看图片
const imgViewVisible = ref(false)
const currentImgIndex = ref(0)
// 获取 el-form 组件上下文
const formContext = inject(formContextKey, void 0)
// 获取 el-form-item 组件上下文
const formItemContext = inject(formItemContextKey, void 0)
// 判断是否禁用上传和删除
const self_disabled = computed(() => {
    return props.disabled || formContext?.disabled
})

const emit = defineEmits(['update:image-urls'])
const handleHttpUpload = async (options) => {
    let formData = new FormData()
    formData.append('file', options.file)
    formData.append('dir', props.dir)
    try {
        const { data } = await request({
            url: props.uploadFileUrl,
            method: 'post',
            headers: {
                'Content-Type': 'multipart/form-data'
            },
            data: formData
        })

        emit('update:image-urls', [...props.imageUrls, data?.fileAccessUrl2])
        // 调用 el-form 内部的校验方法（可自动校验）
        formItemContext?.prop && formContext?.validateField([formItemContext.prop])
    } catch (error) {
        options.onError(error)
    }
}

/**
 * @description 删除图片
 * */
const deleteImg = (index) => {
    const updatedUrls = [...props.imageUrls]
    updatedUrls.splice(index, 1)
    emit('update:image-urls', updatedUrls)
}

/**
 * @description 文件上传之前判断
 * @param rawFile 选择的文件
 * */
const beforeUpload = (rawFile) => {
    const imgSize = rawFile.size / 1024 / 1024 < props.fileSize
    const imgType = props.fileType.includes(rawFile.type)
    if (!imgType)
        ElNotification({
            title: '温馨提示',
            message: '上传图片不符合所需的格式！',
            type: 'warning'
        })
    if (!imgSize)
        setTimeout(() => {
            ElNotification({
                title: '温馨提示',
                message: `上传图片大小不能超过 ${props.fileSize}M！`,
                type: 'warning'
            })
        }, 0)
    return imgType && imgSize
}

/**
 * @description 图片上传成功
 * */
const uploadSuccess = () => {
    ElNotification({
        title: '温馨提示',
        message: '图片上传成功！',
        type: 'success'
    })
}

/**
 * @description 图片上传错误
 * */
const uploadError = () => {
    ElNotification({
        title: '温馨提示',
        message: '图片上传失败，请您重新上传！',
        type: 'error'
    })
}
</script>

<style scoped lang="scss">
.is-error {
    .upload {
        :deep(.el-upload),
        :deep(.el-upload-dragger) {
            border: 1px dashed var(--el-color-danger) !important;

            &:hover {
                border-color: var(--el-color-primary) !important;
            }
        }
    }
}

:deep(.disabled) {
    .el-upload,
    .el-upload-dragger {
        cursor: not-allowed !important;
        background: var(--el-disabled-bg-color);
        border: 1px dashed var(--el-border-color-darker) !important;

        &:hover {
            border: 1px dashed var(--el-border-color-darker) !important;
        }
    }
}

.upload-box {

    display: flex; /* 应用Flex布局 */
    flex-wrap: nowrap; /* 禁止换行 */
    overflow-x: auto; /* 如果内容溢出，添加滚动条 */

    .no-border {

        :deep(.el-upload) {
            border: none !important;
        }
    }

    :deep(.upload) {
        .el-upload {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            width: v-bind(width);
            height: v-bind(height);
            overflow: hidden;
            border: 1px dashed var(--el-border-color-darker);
            border-radius: v-bind(borderRadius);
            transition: var(--el-transition-duration-fast);

            &:hover {
                border-color: var(--el-color-primary);

                .upload-handle {
                    opacity: 1;
                }
            }

            .el-upload-dragger {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 100%;
                padding: 0;
                overflow: hidden;
                background-color: transparent;
                border: 1px dashed var(--el-border-color-darker);
                border-radius: v-bind(borderRadius);

                &:hover {
                    border: 1px dashed var(--el-color-primary);
                }
            }

            .el-upload-dragger.is-dragover {
                background-color: var(--el-color-primary-light-9);
                border: 2px dashed var(--el-color-primary) !important;
            }

            .upload-image {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }

            .upload-empty {
                position: relative;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                line-height: 30px;
                color: var(--el-color-info);

                .el-icon {
                    font-size: 28px;
                    color: var(--el-text-color-secondary);
                }
            }

            .upload-handle {
                position: absolute;
                top: 0;
                right: 0;
                box-sizing: border-box;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 100%;
                cursor: pointer;
                background: rgb(0 0 0 / 60%);
                opacity: 0;
                transition: var(--el-transition-duration-fast);

                .handle-icon {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    padding: 0 6%;
                    color: aliceblue;

                    .el-icon {
                        margin-bottom: 40%;
                        font-size: 130%;
                        line-height: 130%;
                    }

                    span {
                        font-size: 85%;
                        line-height: 85%;
                    }
                }
            }
        }
    }

    .el-upload__tip {
        line-height: 18px;
        text-align: center;
    }

    .upload-box .el-upload:not(:last-child) {
        display: flex; /* 应用Flex布局 */
        flex-wrap: wrap; /* 允许换行 */
        margin-right: 10px !important;
    }

}
</style>
