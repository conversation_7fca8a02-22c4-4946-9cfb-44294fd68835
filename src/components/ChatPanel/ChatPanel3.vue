<template>
    <div class="chat-panel">
        <!-- 聊天头部 -->
        <div class="chat-header">
            <div class="seller-info">
                <img
                    :src="product.companyInfoVo.logo"
                    :alt="product.companyInfoVo.name"
                    class="seller-avatar"
                />
                <div class="seller-details">
                    <h3 class="seller-name">{{ product.companyInfoVo.name }}</h3>
                </div>
            </div>
        </div>

        <!-- 聊天消息区域 -->
        <div ref="messagesContainer" class="chat-messages">
            <div
                v-for="message in messagesList"
                :key="message.id"
                :class="['message', message.questionType]"
            >
                <div v-if="message.questionType === 'received'" class="message-avatar">
                    <img :src="product.companyInfoVo.logo" :alt="product.companyInfoVo.name" />
                </div>

                <div v-if="message.questionType === 'sent'" class="message-avatar">
                    <img :src="defaultAvatar" alt="个人头像" />
                </div>

                <div class="message-content">
                    <div class="message-bubble">
                        <p v-if="message.question">{{ message.question }}</p>
                        <!-- 流式输出的内容 -->
                        <div v-if="message.isStreaming" class="streaming-content">
                            <span class="streaming-text">{{ message.streamingText }}</span>
                            <span v-if="message.isTyping" class="typing-cursor">|</span>
                        </div>
                        <!-- 加载状态 -->
                        <div v-if="message.isLoading" class="loading-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 输入区域 -->
        <div class="chat-input">
            <div class="input-area">
                <el-input
                    ref="messageInput"
                    v-model="inputMessage"
                    type="textarea"
                    :autosize="{ minRows: 1, maxRows: 4 }"
                    placeholder="输入消息..."
                    class="message-input"
                    resize="none"
                    @keydown.enter.prevent="handleSendMessage"
                    @input="adjustTextareaHeight"
                ></el-input>

                <button
                    class="send-btn"
                    :disabled="!inputMessage.trim() || isAiResponding"
                    @click="handleSendMessage"
                >
                    {{ isAiResponding ? '回复中...' : '发送' }}
                </button>
            </div>
        </div>
    </div>
</template>

<script setup>
// Props
import { generateUUID } from '@/utils/index.js'
import { fetchStream } from '@/utils/streamRequest.js'

const props = defineProps({
    product: {
        type: Object,
        default: () => ({}),
    },
})

const defaultAvatar = ref(
    'https://fileso.velora.art/admin/1894995069339000834/5322310f463a437faf3103bc85585d01.png',
)

// 聊天消息
const messagesList = ref([
    {
        conversationId: generateUUID(),
        questionType: 'received',
        question: '您好！欢迎咨询我们的商品，有什么问题可以随时问我哦～',
        timestamp: new Date(Date.now() - 300000),
        objectId: props.product.id,
        type: 1, // 1找产品 2找服务 3找供需 4找商协会 5找企业 11产品详情 12服务详情 13供需对接 21AI小秘书 22AI客服
    },
    {
        conversationId: generateUUID(),
        questionType: 'sent',
        question: '这个商品还有库存吗？',
        timestamp: new Date(Date.now() - 240000),
        objectId: props.product.id,
        type: 1, // 1找产品 2找服务 3找供需 4找商协会 5找企业 11产品详情 12服务详情 13供需对接 21AI小秘书 22AI客服
    },
    {
        conversationId: generateUUID(),
        questionType: 'received',
        question: '有的呢，现在库存充足，您可以放心下单～',
        timestamp: new Date(Date.now() - 180000),
        objectId: props.product.id,
        type: 1, // 1找产品 2找服务 3找供需 4找商协会 5找企业 11产品详情 12服务详情 13供需对接 21AI小秘书 22AI客服
    },
])

// 输入消息
const inputMessage = ref('')
const messagesContainer = ref(null)
const messageInput = ref(null)

// AI 响应状态
const isAiResponding = ref(false)
const currentStreamingMessage = ref(null)

// 发送消息
const handleSendMessage = async () => {
    if (!inputMessage.value.trim() || isAiResponding.value) return

    const userMessage = {
        id: generateUUID(),
        conversationId: generateUUID(),
        questionType: 'sent',
        question: inputMessage.value.trim(),
        timestamp: new Date(),
        objectId: props.product.id,
        type: 11, // 1找产品 2找服务 3找供需 4找商协会 5找企业 11产品详情 12服务详情 13供需对接 21AI小秘书 22AI客服
        tenantId: props.product.tenantId,
    }

    // 添加用户消息
    messagesList.value.push(userMessage)
    inputMessage.value = ''

    // 滚动到底部
    nextTick(() => {
        scrollToBottom()
    })

    // 创建 AI 回复消息
    const aiMessage = {
        id: generateUUID(),
        conversationId: generateUUID(),
        questionType: 'received',
        question: '',
        streamingText: '',
        isStreaming: true,
        isLoading: true,
        isTyping: false,
        timestamp: new Date(),
        objectId: props.product.id,
        type: 1,
        tenantId: props.product.tenantId,
    }

    messagesList.value.push(aiMessage)
    currentStreamingMessage.value = aiMessage
    isAiResponding.value = true

    // 滚动到底部
    nextTick(() => {
        scrollToBottom()
    })

    try {
        // 使用流式请求
        fetchStream(
            userMessage,
            (chunk, allData) => {
                console.log('New chunk:', chunk)
                // 实时更新UI或其他处理
            },
            (completeData) => {
                console.log('Stream completed. All data:', completeData)
            },
            (error) => {
                console.error('Error:', error)
            },
        )
    } catch (error) {
        console.error('AI 对话失败:', error)
    }
}


// 滚动到底部
const scrollToBottom = () => {
    if (messagesContainer.value) {
        messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
}

// 调整输入框高度
const adjustTextareaHeight = () => {
    const textarea = messageInput.value
    if (textarea) {
        console.log('textarea', textarea)
        // textarea.style.height = 'auto'
        // textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px'
    }
}

// 组件挂载后滚动到底部
onMounted(() => {
    scrollToBottom()
})

// 组件卸载时清理
onUnmounted(() => {
    isAiResponding.value = false
    currentStreamingMessage.value = null
})
</script>

<style lang="scss" scoped>
.chat-panel {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #f8f9fa;

    .chat-header {
        padding: 16px;
        background: white;
        border-bottom: 1px solid #e8eaed;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .seller-info {
            display: flex;
            align-items: center;
            gap: 12px;

            .seller-avatar {
                width: 40px;
                height: 40px;
                border-radius: 50%;
            }

            .seller-details {
                .seller-name {
                    font-size: 16px;
                    font-weight: 600;
                    color: #333;
                    margin: 0 0 4px 0;
                }
            }
        }
    }

    .chat-messages {
        flex: 1;
        padding: 16px;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        gap: 16px;

        .message {
            display: flex;
            gap: 8px;

            &.sent {
                flex-direction: row-reverse;

                .message-content {
                    align-items: flex-end;

                    .message-bubble {
                        background: #409eff;
                        color: white;
                    }
                }
            }

            &.received {
                .message-bubble {
                    background: white;
                    color: #333;
                }
            }

            .message-avatar {
                flex-shrink: 0;

                img {
                    width: 32px;
                    height: 32px;
                    border-radius: 50%;
                }
            }

            .message-content {
                display: flex;
                flex-direction: column;
                max-width: 70%;

                .message-bubble {
                    padding: 12px 16px;
                    border-radius: 12px;
                    word-wrap: break-word;

                    p {
                        margin: 0;
                        line-height: 1.4;
                    }

                    // 流式输出内容
                    .streaming-content {
                        .streaming-text {
                            line-height: 1.4;
                        }

                        .typing-cursor {
                            display: inline-block;
                            animation: blink 1s infinite;
                            font-weight: bold;
                            margin-left: 2px;
                        }
                    }

                    // 加载动画
                    .loading-dots {
                        display: flex;
                        gap: 4px;
                        align-items: center;

                        span {
                            width: 6px;
                            height: 6px;
                            border-radius: 50%;
                            background-color: #999;
                            animation: loading-bounce 1.4s infinite ease-in-out both;

                            &:nth-child(1) {
                                animation-delay: -0.32s;
                            }

                            &:nth-child(2) {
                                animation-delay: -0.16s;
                            }

                            &:nth-child(3) {
                                animation-delay: 0s;
                            }
                        }
                    }
                }
            }
        }
    }

    .chat-input {
        background: white;
        border-top: 1px solid #e8eaed;

        .input-area {
            padding: 12px 16px;
            display: flex;
            gap: 12px;
            align-items: flex-end;
            box-sizing: border-box;

            .message-input {
                flex: 1;
                font-size: 14px;

                &:focus {
                    border-color: #409eff;
                }

                &::placeholder {
                    color: #999;
                }
            }

            .send-btn {
                padding: 5px 16px;
                background: #409eff;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;

                &:hover:not(:disabled) {
                    background: #337ecc;
                }

                &:disabled {
                    background: #ccc;
                    cursor: not-allowed;
                }
            }
        }
    }
}

// 动画定义
@keyframes blink {
    0%,
    50% {
        opacity: 1;
    }
    51%,
    100% {
        opacity: 0;
    }
}

@keyframes loading-bounce {
    0%,
    80%,
    100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

// 响应式设计
@media (max-width: 768px) {
    .chat-panel {
        .chat-messages {
            padding: 12px;

            .message {
                .message-content {
                    max-width: 85%;
                }
            }
        }

        .chat-input {
            .input-area {
                padding: 8px 12px;

                .message-input {
                    font-size: 13px;
                }

                .send-btn {
                    font-size: 13px;
                    padding: 6px 12px;
                }
            }
        }
    }
}
</style>
