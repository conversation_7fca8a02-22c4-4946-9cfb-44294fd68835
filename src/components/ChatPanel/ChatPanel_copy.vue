<template>
    <div class="chat-panel">
        <!-- 聊天头部 -->
        <div class="chat-header">
            <div class="seller-info">
                <img :src="sellerInfo.avatar" :alt="sellerInfo.name" class="seller-avatar" />
                <div class="seller-details">
                    <h3 class="seller-name">{{ sellerInfo.name }}</h3>
                    <span class="seller-status" :class="{ online: sellerInfo.isOnline }">
            {{ sellerInfo.isOnline ? '在线' : '离线' }}
          </span>
                </div>
            </div>
            <div class="chat-actions">
                <button class="action-btn" @click="handleVoiceCall">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path
                            d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z" />
                    </svg>
                </button>
                <button class="action-btn" @click="handleVideoCall">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path
                            d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z" />
                    </svg>
                </button>
            </div>
        </div>

        <!-- 聊天消息区域 -->
        <div class="chat-messages" ref="messagesContainer">
            <div v-for="message in messages" :key="message.id" :class="['message', message.type]">
                <div v-if="message.type === 'received'" class="message-avatar">
                    <img :src="sellerInfo.avatar" :alt="sellerInfo.name" />
                </div>

                <div class="message-content">
                    <div class="message-bubble">
                        <p v-if="message.text">{{ message.text }}</p>
                        <img v-if="message.image" :src="message.image" :alt="'图片消息'"
                             class="message-image" />
                        <div v-if="message.product" class="message-product">
                            <img :src="message.product.image" :alt="message.product.title" />
                            <div class="product-info">
                                <h4>{{ message.product.title }}</h4>
                                <p class="product-price">¥{{ message.product.price }}</p>
                            </div>
                        </div>
                    </div>
                    <span class="message-time">{{ formatTime(message.timestamp) }}</span>
                </div>

                <div v-if="message.type === 'sent'" class="message-avatar">
                    <img :src="userInfo.avatar" :alt="userInfo.name" />
                </div>
            </div>
        </div>

        <!-- 快捷回复 -->
        <div class="quick-replies">
            <button
                v-for="reply in quickReplies"
                :key="reply.id"
                class="quick-reply-btn"
                @click="sendQuickReply(reply.text)"
            >
                {{ reply.text }}
            </button>
        </div>

        <!-- 输入区域 -->
        <div class="chat-input">
            <div class="input-toolbar">
                <button class="toolbar-btn" @click="handleEmojiClick">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path
                            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
                    </svg>
                </button>
                <button class="toolbar-btn" @click="handleImageUpload">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path
                            d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z" />
                    </svg>
                </button>
                <button class="toolbar-btn" @click="sendProductCard">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path
                            d="M7 18c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z" />
                    </svg>
                </button>
            </div>

            <div class="input-area">
        <textarea
            v-model="inputMessage"
            placeholder="输入消息..."
            class="message-input"
            rows="1"
            @keydown.enter.prevent="handleSendMessage"
            @input="adjustTextareaHeight"
            ref="messageInput"
        ></textarea>
                <button
                    class="send-btn"
                    :disabled="!inputMessage.trim()"
                    @click="handleSendMessage"
                >
                    发送
                </button>
            </div>
        </div>
    </div>
</template>

<script setup>
// Props
const props = defineProps({
    product: {
        type: Object,
        default: () => ({})
    }
})

// 卖家信息
const sellerInfo = ref({
    id: 1,
    name: '优品小店',
    avatar: 'https://file2test.velora.art/woagent/jpg/6864957542ba8cf4e1063877.jpg',
    isOnline: true
})

// 用户信息
const userInfo = ref({
    id: 2,
    name: '我',
    avatar: 'https://file2test.velora.art/woagent/png/685cc4bf42bacfe5c7b6d14e.png'
})

// 聊天消息
const messages = ref([
    {
        id: 1,
        type: 'received',
        text: '您好！欢迎咨询我们的商品，有什么问题可以随时问我哦～',
        timestamp: new Date(Date.now() - 300000)
    },
    {
        id: 2,
        type: 'sent',
        text: '这个商品还有库存吗？',
        timestamp: new Date(Date.now() - 240000)
    },
    {
        id: 3,
        type: 'received',
        text: '有的呢，现在库存充足，您可以放心下单～',
        timestamp: new Date(Date.now() - 180000)
    }
])

// 快捷回复
const quickReplies = ref([
    { id: 1, text: '有优惠吗？' },
    { id: 2, text: '什么时候发货？' },
    { id: 3, text: '支持退换吗？' },
    { id: 4, text: '包邮吗？' }
])

// 输入消息
const inputMessage = ref('')
const messagesContainer = ref(null)
const messageInput = ref(null)

// 发送消息
const handleSendMessage = () => {
    if (!inputMessage.value.trim()) return

    const newMessage = {
        id: Date.now(),
        type: 'sent',
        text: inputMessage.value.trim(),
        timestamp: new Date()
    }

    messages.value.push(newMessage)
    inputMessage.value = ''

    // 滚动到底部
    nextTick(() => {
        scrollToBottom()
    })

    // 模拟自动回复
    setTimeout(() => {
        const autoReply = {
            id: Date.now() + 1,
            type: 'received',
            text: '好的，我来为您详细介绍一下～',
            timestamp: new Date()
        }
        messages.value.push(autoReply)
        nextTick(() => {
            scrollToBottom()
        })
    }, 1000)
}

// 发送快捷回复
const sendQuickReply = (text) => {
    inputMessage.value = text
    handleSendMessage()
}

// 发送商品卡片
const sendProductCard = () => {
    const productMessage = {
        id: Date.now(),
        type: 'sent',
        product: {
            title: props.product.title || '商品名称',
            price: props.product.price || 99,
            image: props.product.image || 'https://via.placeholder.com/60x60'
        },
        timestamp: new Date()
    }

    messages.value.push(productMessage)
    nextTick(() => {
        scrollToBottom()
    })
}

// 滚动到底部
const scrollToBottom = () => {
    if (messagesContainer.value) {
        messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
}

// 调整输入框高度
const adjustTextareaHeight = () => {
    const textarea = messageInput.value
    if (textarea) {
        textarea.style.height = 'auto'
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px'
    }
}

// 格式化时间
const formatTime = (timestamp) => {
    const now = new Date()
    const time = new Date(timestamp)
    const diff = now - time

    if (diff < 60000) { // 1分钟内
        return '刚刚'
    } else if (diff < 3600000) { // 1小时内
        return Math.floor(diff / 60000) + '分钟前'
    } else if (diff < 86400000) { // 24小时内
        return Math.floor(diff / 3600000) + '小时前'
    } else {
        return time.toLocaleDateString()
    }
}

// 事件处理
const handleVoiceCall = () => {
    console.log('发起语音通话')
}

const handleVideoCall = () => {
    console.log('发起视频通话')
}

const handleEmojiClick = () => {
    console.log('选择表情')
}

const handleImageUpload = () => {
    console.log('上传图片')
}

// 组件挂载后滚动到底部
onMounted(() => {
    scrollToBottom()
})
</script>

<style lang="scss" scoped>
.chat-panel {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #f8f9fa;

    .chat-header {
        padding: 16px;
        background: white;
        border-bottom: 1px solid #e8eaed;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .seller-info {
            display: flex;
            align-items: center;
            gap: 12px;

            .seller-avatar {
                width: 40px;
                height: 40px;
                border-radius: 50%;
            }

            .seller-details {
                .seller-name {
                    font-size: 16px;
                    font-weight: 600;
                    color: #333;
                    margin: 0 0 4px 0;
                }

                .seller-status {
                    font-size: 12px;
                    color: #999;

                    &.online {
                        color: #52c41a;
                    }
                }
            }
        }

        .chat-actions {
            display: flex;
            gap: 8px;

            .action-btn {
                width: 32px;
                height: 32px;
                border: none;
                background: #f0f0f0;
                border-radius: 6px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                color: #666;
                transition: all 0.2s ease;

                &:hover {
                    background: #e0e0e0;
                    color: #333;
                }
            }
        }
    }

    .chat-messages {
        flex: 1;
        padding: 16px;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        gap: 16px;

        .message {
            display: flex;
            gap: 8px;

            &.sent {
                flex-direction: row-reverse;

                .message-content {
                    align-items: flex-end;

                    .message-bubble {
                        background: #409eff;
                        color: white;
                    }
                }
            }

            &.received {
                .message-bubble {
                    background: white;
                    color: #333;
                }
            }

            .message-avatar {
                flex-shrink: 0;

                img {
                    width: 32px;
                    height: 32px;
                    border-radius: 50%;
                }
            }

            .message-content {
                display: flex;
                flex-direction: column;
                max-width: 70%;

                .message-bubble {
                    padding: 12px 16px;
                    border-radius: 12px;
                    word-wrap: break-word;

                    p {
                        margin: 0;
                        line-height: 1.4;
                    }

                    .message-image {
                        max-width: 200px;
                        border-radius: 8px;
                    }

                    .message-product {
                        display: flex;
                        gap: 12px;
                        padding: 8px;
                        background: rgba(255, 255, 255, 0.1);
                        border-radius: 8px;

                        img {
                            width: 60px;
                            height: 60px;
                            border-radius: 6px;
                            object-fit: cover;
                        }

                        .product-info {
                            flex: 1;

                            h4 {
                                font-size: 14px;
                                margin: 0 0 4px 0;
                                color: inherit;
                            }

                            .product-price {
                                font-size: 16px;
                                font-weight: 600;
                                color: #ff4757;
                                margin: 0;
                            }
                        }
                    }
                }

                .message-time {
                    font-size: 12px;
                    color: #999;
                    margin-top: 4px;
                    padding: 0 4px;
                }
            }
        }
    }

    .quick-replies {
        padding: 12px 16px;
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
        background: white;
        border-top: 1px solid #f0f0f0;

        .quick-reply-btn {
            padding: 6px 12px;
            border: 1px solid #e0e0e0;
            background: white;
            border-radius: 16px;
            font-size: 12px;
            color: #666;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
                border-color: #409eff;
                color: #409eff;
            }
        }
    }

    .chat-input {
        background: white;
        border-top: 1px solid #e8eaed;

        .input-toolbar {
            padding: 8px 16px;
            display: flex;
            gap: 8px;
            border-bottom: 1px solid #f0f0f0;

            .toolbar-btn {
                width: 32px;
                height: 32px;
                border: none;
                background: none;
                border-radius: 6px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                color: #666;
                transition: all 0.2s ease;

                &:hover {
                    background: #f0f0f0;
                    color: #333;
                }
            }
        }

        .input-area {
            padding: 12px 16px;
            display: flex;
            gap: 12px;
            align-items: flex-end;

            .message-input {
                flex: 1;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                resize: none;
                outline: none;
                min-height: 36px;
                max-height: 120px;
                line-height: 1.4;

                &:focus {
                    border-color: #409eff;
                }

                &::placeholder {
                    color: #999;
                }
            }

            .send-btn {
                padding: 8px 16px;
                background: #409eff;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;

                &:hover:not(:disabled) {
                    background: #337ecc;
                }

                &:disabled {
                    background: #ccc;
                    cursor: not-allowed;
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .chat-panel {
        .chat-messages {
            padding: 12px;

            .message {
                .message-content {
                    max-width: 85%;
                }
            }
        }

        .quick-replies {
            padding: 8px 12px;

            .quick-reply-btn {
                font-size: 11px;
                padding: 4px 8px;
            }
        }

        .chat-input {
            .input-area {
                padding: 8px 12px;

                .message-input {
                    font-size: 13px;
                }

                .send-btn {
                    font-size: 13px;
                    padding: 6px 12px;
                }
            }
        }
    }
}
</style>
