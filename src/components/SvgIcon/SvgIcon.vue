<template>
    <svg class="svg-icon" :class="className" aria-hidden="true" v-bind="$attrs"
         :style="{ width: size,height: size,color: color}">
        <use :xlink:href="`#icon-${name}`" />
    </svg>
</template>

<script setup>
defineProps({
    name: {
        type: String,
        required: true
    },
    size: {
        type: String,
        default: '1em'
    },
    color: {
        type: String,
        default: 'currentColor'
    },
    className: {
        type: String,
        default: ''
    }
})
</script>

<style scoped>
.svg-icon {
    display: inline-block;
    vertical-align: middle;
    fill: currentColor;
    overflow: hidden;
}
</style>
