<template>
    <div class="supply-demand-item" @click="handleItemClick(item)">
        <!-- 商品图片 -->
        <div class="item-image">
            <img :src="item.mainImg" :alt="item.title" />
            <!-- 供需标签 -->
            <div class="supply-demand-tag" :class="item.type">
                {{ item.type === 1 ? '供应' : '需求' }}
            </div>
        </div>

        <!-- 商品信息 -->
        <div class="item-content">
            <!-- 标题 -->
            <div class="item-title">{{ item.title }}</div>

            <!-- 底部信息 -->
            <div class="item-footer">
                <!-- 价格 -->
                <div class="price-section flex-row jc-space-between">
                    <div class="_left flex-row ai-center">
                        <div class="current-price" v-if="item.saleType === 1">
                            ¥{{ item.price }}
                        </div>
                        <div class="current-price" v-else>面议</div>
                    </div>
                    <div class="_right flex-row ai-center">
                        <div class="_txt">浏览量</div>
                        <div class="_num">{{ item.pointNum }}</div>
                    </div>
                </div>

                <div class="item-cate flex-row ai-center">
                    <div class="_span">{{ item.cateName }}</div>
                </div>

                <!-- 用户信息和时间 -->
                <div class="company-info">
                    <div class="_left flex-row ai-center">
                        <div class="_logo">
                            <img :src="item.companyInfo.logo" :alt="item.companyInfo.name" />
                        </div>
                        <div class="_name text-over">{{ item.companyInfo.name }}</div>
                    </div>
                    <div class="publish-time">{{ handleTime(item.createTime) }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
// 定义props
defineProps({
    item: {
        type: Object,
        required: false,
        default: () => ({}),
    },
})

const emit = defineEmits(['item-click'])

const handleTime = (dateStr) => {
    if (dateStr) {
        return dateStr.match(/^\d{4}-\d{2}-\d{2}/)[0]
    } else {
        return ''
    }
}

const handleItemClick = (item) => {
    emit('item-click', item)
}
</script>

<style scoped lang="scss">
.supply-demand-item {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    transition:
        transform 0.3s ease,
        box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;
    height: auto;

    &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);

        .item-image img {
            transform: scale(1.05);
        }
    }

    .item-image {
        position: relative;
        width: 100%;
        height: 180px;
        overflow: hidden;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .supply-demand-tag {
            position: absolute;
            top: 12px;
            left: 12px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            color: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            background: red;

            &.supply {
                background: linear-gradient(135deg, #4caf50, #66bb6a);
            }

            &.demand {
                background: linear-gradient(135deg, #ff9800, #ffb74d);
            }
        }
    }

    .item-content {
        padding: 16px;
        display: flex;
        flex-direction: column;
        gap: 12px;
        flex: 1;

        .item-title {
            font-size: 15px;
            font-weight: 600;
            color: #333;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            margin: 0;
        }

        .item-footer {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-top: auto;

            .price-section {
                display: flex;
                align-items: baseline;
                gap: 8px;
                margin-bottom: 8px;

                ._left {
                    gap: 5px;

                    .current-price {
                        color: #ff4757;
                        font-size: 20px;
                        font-weight: bold;
                    }
                }

                ._right {
                    gap: 5px;
                    font-size: 12px;
                    color: #999;
                }
            }

            .item-cate {
                ._span {
                    font-size: 12px;
                    background: #3d8af5;
                    color: #fff;
                    padding: 2px 10px;
                    border-radius: 5px;
                }
            }

            .company-info {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 8px 0;
                border-top: 1px solid #f0f0f0;
                font-size: 12px;

                ._left {
                    width: calc(100% - 70px);
                    gap: 5px;

                    ._logo {
                        width: 20px;
                        height: 20px;
                        border-radius: 50%;
                        overflow: hidden;

                        img {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                        }
                    }

                    ._name {
                        width: calc(100% - 25px);
                        color: #666;
                        font-weight: 500;
                    }
                }

                .publish-time {
                    width: 62px;
                    color: #999;
                    font-size: 11px;
                }
            }
        }
    }
}
</style>
