<template>
    <el-dialog
        v-model="dialogVisible"
        title="请扫码支付"
        width="400"
        :append-to-body="false"
        @close="handleOver"
    >
        <div class="qr-code-container flex-column jc-center ai-center">
            <canvas ref="qrCanvas"></canvas>
            <p v-if="loading">生成二维码中...</p>
            <p v-if="error">{{ error }}</p>
            <el-button plain type="primary" size="small" @click.stop="handleOver"
                >支付完成
            </el-button>
            <div class="_tips">请用微信扫码支付</div>
        </div>
    </el-dialog>
</template>

<script setup>
import QRCode from 'qrcode'
import { getPayDetail } from '@/api/order.js'

const dialogVisible = ref(false)
const qrCanvas = ref(null)
const loading = ref(false)
const error = ref(null)

const payInfo = ref('')

const props = defineProps({
    options: {
        type: Object,
        default: () => ({}),
    },
    payOrderNo: {
        type: String,
        default: '',
    },
})

const emit = defineEmits(['pay-over'])

const openDialog = (info) => {
    dialogVisible.value = true
    payInfo.value = info
}

const generateQRCode = () => {
    if (!payInfo.value) return

    nextTick(() => {
        loading.value = true
        error.value = null

        QRCode.toCanvas(qrCanvas.value, payInfo.value, props.options, (err) => {
            loading.value = false
            if (err) {
                error.value = '生成二维码失败: ' + err.message
                console.error(err)
            }
        })
    })
}

const handleOver = async (event) => {
    // 如果点击的是 支付完成，其实传播了2次事件
    dialogVisible.value = false
    if (!event) {
        try {
            let params = {
                payOrderNo: props.payOrderNo,
            }
            const response = await getPayDetail(params)
            console.log('订单支付详情', response)
            emit('pay-over', response.data)
        } catch (e) {
            console.log('关闭窗口失败', e)
        }
    }
}

// 初始生成
onMounted(generateQRCode)

// 监听 payInfo 变化重新生成
watch(() => payInfo.value, generateQRCode)

defineExpose({
    openDialog,
})
</script>

<style scoped>
.qr-code-container {
    gap: 10px;
    height: 300px;

    ._tips {
        font-size: 12px;
        color: #939099;
    }
}
</style>
