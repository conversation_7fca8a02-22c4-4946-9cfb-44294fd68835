<template>
    <div class="flex-row ai-center category-wrap">
        <!-- 左箭头 -->
        <div
            class="_item flex-row jc-center ai-center"
            @click="scrollLeft"
            :class="{ disabled: isAtStart }"
        >
            <svg-icon name="zuo"></svg-icon>
        </div>

        <!-- 滚动区域 -->
        <el-scrollbar
                ref="scrollbarRef"
            class="hide-scrollbar category-scrollbar"
            @scroll="handleScroll"
        >
            <div ref="contentRef" class="category-list">
                <div
                    v-for="item in list"
                    :key="item.id"
                    class="category-item flex-column jc-center ai-center"
                    :class="{ _active: selectId === item.id }"
                    @click="handleCate(item)"
                >
                    <template v-if="item.img">
                        <img class="_icon" :src="item.img" :alt="item.name" />
                    </template>
                    <template v-else>
                        <svg-icon :name="item.icon" color="#626066" size="24"></svg-icon>
                    </template>
                    <div class="_label">{{ item.name }}</div>
                </div>
            </div>
        </el-scrollbar>

        <!-- 右箭头 -->
        <div
            class="_item flex-row jc-center ai-center"
            @click="scrollRight"
            :class="{ disabled: isAtEnd }"
        >
            <svg-icon name="you"></svg-icon>
        </div>
    </div>
</template>

<script setup>
import SvgIcon from '@/components/SvgIcon/SvgIcon.vue'

defineProps({
    list: {
        type: Array,
        default: () => [],
    },
})

const emit = defineEmits(['cate'])

const scrollbarRef = ref(null)
const contentRef = ref(null)
const isAtStart = ref(true)
const isAtEnd = ref(false)
const containerWidth = ref(0)
const selectId = ref(null)

// 获取滚动容器元素
const getScrollContainer = () => {
    return scrollbarRef.value?.$el.querySelector('.el-scrollbar__wrap')
}

// 检查是否有可滚动内容
const hasScrollableContent = () => {
    const scrollContainer = getScrollContainer()
    if (!scrollContainer || !contentRef.value) return false
    return contentRef.value.scrollWidth > scrollContainer.clientWidth
}

// 更新滚动位置状态
const updateScrollPosition = () => {
    const scrollContainer = getScrollContainer()
    if (!scrollContainer) return

    containerWidth.value = scrollContainer.clientWidth
    const { scrollLeft, scrollWidth } = scrollContainer
    const tolerance = 1 // 容差，避免小数误差

    isAtStart.value = scrollLeft <= tolerance
    isAtEnd.value = scrollLeft >= scrollWidth - containerWidth.value - tolerance

    // 如果没有可滚动内容，两个状态都为true
    if (!hasScrollableContent()) {
        isAtStart.value = true
        isAtEnd.value = true
    }
}

// 滚动事件处理
const handleScroll = () => {
    updateScrollPosition()
}

// 滚动方法 - 按容器宽度的60%滚动
const scrollByPercentage = (direction) => {
    const scrollContainer = getScrollContainer()
    if (!scrollContainer) return

    const scrollAmount = containerWidth.value * 0.6 * direction
    scrollContainer.scrollBy({
        left: scrollAmount,
        behavior: 'smooth',
    })
}

const scrollLeft = () => {
    if (!isAtStart.value) scrollByPercentage(-1) // 向左滚动60%
}

const scrollRight = () => {
    if (!isAtEnd.value) scrollByPercentage(1) // 向右滚动60%
}

const handleCate = (item) => {
    if (item.id !== selectId.value) {
        selectId.value =  item.id
        emit('cate', selectId.value ? item : null)
    }
}


let observer
onMounted(() => {
    nextTick(() => {
        updateScrollPosition()
        // 添加resize监听
        window.addEventListener('resize', updateScrollPosition)
        // 添加MutationObserver监听内容变化
        observer = new MutationObserver(updateScrollPosition)
        if (contentRef.value) {
            observer.observe(contentRef.value, {
                childList: true,
                subtree: true,
                attributes: true,
            })
        }
    })
})

onUnmounted(() => {
    window.removeEventListener('resize', updateScrollPosition)
    observer.disconnect()
})
</script>

<style scoped lang="scss">
.category-wrap {
    width: 100%;
    gap: 10px;
    position: relative;

    ._item {
        width: 32px;
        height: 32px;
        background: #fff;
        border-radius: 20px;
        border: 1px solid #626066;
        cursor: pointer;
        transition: all 0.3s ease;
        flex-shrink: 0;

        &:hover:not(.disabled) {
            transform: scale(1.05);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        &.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            border-color: #ccc;
        }
    }

    .category-scrollbar {
        max-width: calc(100% - 100px);
        flex: 1;

        .el-scrollbar__wrap {
            overflow-x: auto;
            overflow-y: hidden;
        }

        .category-list {
            display: flex;
            width: fit-content;
            gap: 15px;
            padding: 5px 0;

            .category-item {
                flex-shrink: 0;
                gap: 5px;
                //width: 60px;
                cursor: pointer;

                ._icon {
                    width: 24px;
                    height: 24px;
                    object-fit: contain;
                }

                ._label {
                    font-size: 12px;
                    color: #313033;
                    white-space: nowrap;
                    text-align: center;
                }

                &._active {
                    ._label {
                        color: #1a73e8;
                        font-weight: 550;
                    }
                }
            }
        }
    }
}
</style>
