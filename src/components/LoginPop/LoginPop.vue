<template>
    <el-dialog v-model="dialogVisible" width="750" :destroy-on-close="true">
        <div class="login-box flex-row">
            <div class="login-item flex-column jc-center ai-center">
                <div class="left-title">微信扫码登录</div>
                <div class="qrcode">
                    <el-image
                        class="qrcode-img"
                        src="https://shuziyunxie-1313466856.cos.ap-guangzhou.myqcloud.com/picture_url/bb0f37c5-3393-4d8f-a823-6503ea3b576d.png"
                    >
                    </el-image>
                </div>
                <div class="_tips">请使用微信扫码</div>
            </div>
            <div class="login-item flex-column jc-center ai-center">
                <div class="right-title">手机号登录</div>
                <div class="form-box">
                    <div class="_item">
                        <input placeholder="请输入手机号" v-model="form.phone" />
                    </div>
                    <div class="_item">
                        <input placeholder="请输入验证码" maxlength="6" v-model="form.smsCode" />
                        <span class="_label" @click="getCode()">{{ buttonText }}</span>
                    </div>
                    <div class="_item">
                        <el-button type="primary" @click="onSubmit()">登录</el-button>
                    </div>
                </div>
                <div class="form-tip flex-row jc-flex-start ai-center jc-center">
                    <div class="_checked flex-row ai-center">
                        <input v-model="isAgree" type="checkbox" />
                    </div>
                    <div class="xy">
                        <el-text>我已阅读并同意</el-text>
                        <el-text type="primary">《用户协议》</el-text>
                        <el-text type="primary">《隐私政策》</el-text>
                    </div>
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<script setup>
import { validatePhone } from '@/utils/validate.js'
import { getSmsCode, loginBySms } from '@/api/main.js'
import { useMessage } from '@/utils/useMessage.js'
import { useUserStore } from '@/stores/user.js'
import { useRouter } from 'vue-router'
import { useCartStore } from '@/stores/shop-cart.js'

const userStore = useUserStore()
const router = useRouter()

const dialogVisible = ref(false)
const totalTime = ref(60)
const buttonText = ref('获取验证码')

const isDisabled = ref(false)
const isAgree = ref(true)

// 存储登录成功后需要跳转的路径
const redirectPath = ref('')

const form = reactive({
    smsCode: '137666',
    phone: '',
})

const openDialog = (redirect = '') => {
    dialogVisible.value = true
    redirectPath.value = redirect
}

const getCode = () => {
    if (isDisabled.value) {
        return
    }

    if (validatePhone(form.phone)) {
        let params = {
            phone: form.phone,
        }
        isDisabled.value = true
        buttonText.value = '还剩' + totalTime.value + '秒'
        getSmsCode(params).then((response) => {
            console.log('response', response)
            useMessage().success('验证码已发送')
        })

        let timer = setInterval(() => {
            if (totalTime.value > 1) {
                totalTime.value--
                buttonText.value = '还剩' + totalTime.value + '秒'
            } else {
                totalTime.value = 60
                isDisabled.value = false
                buttonText.value = '获取验证码'
                clearInterval(timer)
            }
        }, 1000)
    }
}

const onSubmit = () => {
    if (!isAgree.value) {
        return useMessage().error('请勾选隐私协议选项')
    }

    if (validatePhone(form.phone)) {
        loginBySms(form).then((response) => {
            let userInfo = response
            userStore.setToken(userInfo['access_token'])
            userStore.setUserInfo(userInfo['user_info'])
            useMessage().success('登录成功')
            dialogVisible.value = false

            // 登录成功以后要重新加载购物车商品数量
            const cart = useCartStore()
            cart.getCount()

            // 登录成功后的处理
            if (redirectPath.value) {
                // 有跳转路径，进行页面跳转
                router.push(redirectPath.value)
                redirectPath.value = '' // 清空跳转路径
            } else {
                // 无跳转路径，只触发登录成功事件，让调用方自行处理
                window.dispatchEvent(
                    new CustomEvent('login-success', {
                        detail: {
                            userInfo: userInfo['user_info'],
                            token: userInfo['access_token'],
                        },
                    }),
                )
            }
        })
    }
}

// 监听全局登录事件
const handleShowLoginDialog = (event) => {
    const { redirectPath: redirect } = event.detail || {}
    openDialog(redirect)
}

// 组件挂载时添加事件监听
onMounted(() => {
    window.addEventListener('show-login-dialog', handleShowLoginDialog)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
    window.removeEventListener('show-login-dialog', handleShowLoginDialog)
})

// 暴露变量
defineExpose({
    openDialog,
})
</script>

<style scoped lang="scss">
.login-box {
    width: 100%;
    height: 400px;

    .login-item {
        width: calc(50% - 1px);
        gap: 12px;

        &:last-child {
            border-left: 1px solid #ededed;
        }

        .left-title {
            width: 220px;
            height: 48px;
            line-height: 48px;
            text-align: center;
            border-radius: 24px;
            color: #3d8af5;
            background: rgba(61, 138, 245, 0.1);
            font-size: 16px;
            font-weight: 600;
        }

        .right-title {
            width: 220px;
            height: 48px;
            line-height: 48px;
            text-align: center;
            border-radius: 24px;
            color: #000;
            font-size: 16px;
            font-weight: 600;
        }

        .qrcode {
            width: 200px;
            height: 200px;
            padding: 20px;
            box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
            border-radius: 15px;
            margin: 20px 0;

            .qrcode-img {
                width: 100%;
                height: 100%;
            }
        }

        .form-box {
            width: 300px;

            ._item {
                width: 100%;
                height: 48px;
                background: rgba(0, 0, 0, 0.03);
                margin-bottom: 20px;
                padding: 0 12px;
                border-radius: 24px;
                position: relative;

                input {
                    width: 100%;
                    height: 100%;
                    caret-color: #409eff;
                }

                ._label {
                    position: absolute;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    padding: 0 12px;
                    line-height: 48px;
                    color: #409eff;
                    cursor: pointer;
                    font-size: 16px;
                }

                .el-button {
                    width: 100%;
                    height: 48px;
                    border-radius: 24px;
                }

                &:last-child {
                    padding: 0;
                    margin-top: 32px;
                }
            }
        }

        .form-tip {
            width: 300px;

            ._checked {
                width: 20px;
                height: 20px;

                input[type='checkbox'] {
                    width: 15px;
                    height: 15px;
                    margin-bottom: -2px;
                }
            }

            .xy {
                width: calc(100% - 30px);

                .el-text {
                    cursor: pointer;
                }
            }
        }
    }
}
</style>
