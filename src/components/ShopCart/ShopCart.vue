<template>
    <el-drawer v-model="visible" title="购物车" size="60%" @close="closeDrawer">
        <!-- 购物车内容 -->
        <el-scrollbar class="hide-scrollbar">
            <div class="cart-content">
                <!-- 购物车头部操作栏 -->
                <el-card class="cart-header">
                    <div class="flex-row ai-center _header">
                        <div class="select-all">
                            <el-checkbox
                                v-model="isAllSelected"
                                size="large"
                                @change="toggleAllSelection"
                            >
                                全选
                            </el-checkbox>
                        </div>
                        <div class="goods-name">商品</div>
                        <div class="goods-other">单价</div>
                        <div class="goods-other">数量</div>
                        <div class="goods-other">操作</div>
                    </div>
                </el-card>

                <!-- 购物车商品列表 -->
                <el-card v-for="item in cartList" :key="item.id" class="cart-items">
                    <template #header>
                        <div class="shop-name flex-row ai-center">
                            <div class="_item">
                                <el-image :src="item.logo"></el-image>
                            </div>
                            <div class="_item">
                                <div class="_label">{{ item.name }}</div>
                            </div>
                        </div>
                    </template>
                    <cart-item
                        v-for="child in item.children"
                        :key="child?.id"
                        :item="child"
                        :tenant-id="item.tenantId"
                    >
                    </cart-item>
                </el-card>

                <el-card v-if="cartList.length === 0">
                    <div class="no-data">购物车空空如也</div>
                </el-card>

                <!-- 结算面板 -->
                <el-card v-if="cartList.length > 0" class="checkout-panel">
                    <div class="flex-row jc-space-between ai-center">
                        <div class="_left flex-row ai-center">
                            <el-checkbox
                                v-model="isAllSelected"
                                size="small"
                                @change="toggleAllSelection"
                            >
                                全选
                            </el-checkbox>
                            <div class="_del" @click="handleDelSelected">删除选中商品</div>
                            <div class="_del" @click="handleClearSelected">清空购物车</div>
                        </div>
                        <div class="checkout-summary flex-row ai-center">
                            <div class="summary-info flex-row ai-center">
                                <div class="selected-items flex-row ai-center">
                                    <div class="_label">已选择</div>
                                    <div class="_label _red">{{ multipleSelection.length }}</div>
                                    <div class="_label">件商品</div>
                                </div>
                                <div class="total-price flex-row ai-center">
                                    <span class="label">总价：</span>
                                    <span class="price">¥{{ totalPrice }}</span>
                                </div>
                            </div>

                            <div class="checkout-actions">
                                <el-button
                                    type="primary"
                                    class="checkout-btn"
                                    @click="handleCheckout"
                                >
                                    去结算
                                </el-button>
                            </div>
                        </div>
                    </div>
                </el-card>
            </div>
        </el-scrollbar>

        <good-place-pop ref="orderPlaceRef"></good-place-pop>
    </el-drawer>
</template>

<script setup>
import { useCartStore } from '@/stores/shop-cart.js'
import { useMessage } from '@/utils/useMessage.js'
import { ElMessageBox } from 'element-plus'
import CartItem from '@/components/ShopCart/CartItem.vue'
import GoodPlacePop from '@/components/OrderPlacePop/GoodPlacePop.vue'

const cart = useCartStore()

// Emits
const emit = defineEmits(['close'])

const isAllSelected = ref(false)
const visible = ref(false)
const multipleSelection = ref([])
const totalPrice = ref(0)

// 确认订单
const orderPlaceRef = ref(null)

const openDrawer = () => {
    visible.value = true
}

const closeDrawer = () => {
    visible.value = false
    //关闭的时候重新加载购物车数量
    cart.getCount()
    emit('close')
}

const cartList = computed(() => {
    // 深拷贝原始数据
    return JSON.parse(JSON.stringify(cart.items))
})

// 使用 $patch 深度修改
const toggleAllSelection = (bool) => {
    nextTick(() => {
        cart.$patch((state) => {
            state.items.forEach((item) => {
                item.children.forEach((child) => {
                    child.selected = bool
                })
            })
        })
    })
}

// 删除选择的商品
const handleDelSelected = async () => {
    if (multipleSelection.value.length === 0) {
        return useMessage().error('请至少选中一件商品！')
    }

    try {
        await ElMessageBox.confirm(`确定要删除选中的商品吗？`, '确认删除', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })

        await cart.clearCart()
        useMessage().success('删除成功')
    } catch {
        // 用户取消删除
    }
}

// 清空购物车
const handleClearSelected = async () => {
    try {
        await ElMessageBox.confirm(`确定要删除购物车里面的所有商品吗？`, '确认删除', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })

        let productIds = []
        console.log('cartList.value', cartList.value)
        cartList.value.forEach((item) => {
            item.children.forEach((child) => {
                productIds.push(child.id)
            })
        })
        await cart.removeFromCart(productIds)
    } catch {
        // 用户取消删除
    }
}

/**
 * 计算购物车商品总价（解决浮点数精度问题，保留2位小数）
 * @param {Array} items 商品数组
 * @returns {Number} 总价，保留2位小数
 */
function calculateTotal(items) {
    //  1. 计算总价（以分为单位计算避免浮点误差）
    const totalInCents = items.reduce((sum, item) => {
        const priceInCents = Math.round(item.buyPrice * 100) // 价格转为分
        return sum + priceInCents * item.num
    }, 0)

    // 2. 转换回元并保留2位小数
    const total = totalInCents / 100

    // 3. 确保输出保留2位小数（如830.6显示为830.60）
    return parseFloat(total.toFixed(2)) > 0 ? parseFloat(total.toFixed(2)) : '0.00'
}

// 去结算按钮
const handleCheckout = async () => {
    console.log('handleCheckout 被调用SS') // 查看是谁调用了这个方法
    console.trace('handleCheckout 被调用') // 查看是谁调用了这个方法
    if (multipleSelection.value.length === 0) {
        return useMessage().error('请至少选中一件商品！')
    }

    let goodsList = []
    let params = {
        userAddressId: '',
        goodsList: [],
    }

    multipleSelection.value.forEach((n) => {
        goodsList.push({
            goodsId: n.goodsId,
            num: n.num,
            shopId: n.shopId,
            carId: n.id,
        })
    })
    params.goodsList = goodsList
    console.log('结算参数params', params)

    try {
        orderPlaceRef.value.openDrawer(params)
    } catch (e) {
        console.log('结算发生了错误', e)
    }
}

// 监听购物车数据变化
watch(
    () => cart.items,
    (newVal) => {
        multipleSelection.value = []
        newVal.forEach((item) => {
            item.children.forEach((child) => {
                if (child.selected) {
                    multipleSelection.value.push(child)
                }
            })
        })
        totalPrice.value = calculateTotal(multipleSelection.value)
        console.log('multipleSelection.value', multipleSelection.value)
    },
    {
        deep: true,
        immediate: true,
    },
)

onMounted(() => {
    cart.loadCart()
})

defineExpose({
    openDrawer,
})
</script>

<style scoped lang="scss">
.cart-content {
    .el-card {
        :deep(.el-card__body) {
            padding: 10px 20px;
        }
    }

    .cart-header {
        position: sticky;
        top: 0;
        z-index: 2;
        margin-bottom: 5px;
        border-radius: 0;

        .el-card__body {
            padding-bottom: 3px;
            padding-top: 3px;
        }

        ._header {
            gap: 16px;

            .select-all {
                width: 130px;
            }

            .goods-name {
                flex-grow: 1;
            }

            .goods-other {
                width: 150px;
                text-align: center;
                font-size: 13px;
                color: #666;

                &:last-child {
                    width: 80px;
                    margin-right: 0;
                }
            }
        }
    }

    .cart-items {
        margin-bottom: 8px;

        :deep(.el-card__header) {
            padding-top: 3px;
            padding-bottom: 3px;
        }

        .shop-name {
            gap: 10px;

            ._item {
                .el-image {
                    height: 30px;
                    object-fit: contain;
                    border-radius: 5px;
                }

                ._label {
                    font-size: 14px;
                    color: #000;
                    font-weight: 550;
                }
            }
        }
    }

    .checkout-panel {
        position: sticky;
        bottom: 0;
        z-index: 10;

        ._left {
            gap: 10px;
            font-size: 12px;
            color: #999;
            cursor: pointer;

            ._del {
                &:hover {
                    color: #e74c3c;
                }
            }

            .el-checkbox {
                font-size: 12px;
                color: #666;
                margin-right: 12px;
            }
        }

        .checkout-summary {
            gap: 20px;

            .summary-info {
                gap: 24px;

                .selected-items {
                    gap: 5px;

                    ._label {
                        color: #666;
                        font-size: 14px;

                        &._red {
                            color: #e74c3c;
                            font-weight: 550;
                        }
                    }
                }

                .total-price {
                    gap: 3px;

                    .label {
                        font-size: 14px;
                        color: #333;
                    }

                    .price {
                        font-size: 14px;
                        font-weight: 600;
                        color: #e74c3c;
                    }
                }
            }

            .checkout-actions {
                .checkout-btn {
                    padding: 12px 32px;
                    font-size: 16px;
                    font-weight: 600;
                }
            }
        }
    }

    .no-data {
        font-size: 14px;
        color: #999;
        text-align: center;
        padding: 20px 0;
    }
}
</style>
