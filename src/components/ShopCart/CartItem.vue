<template>
    <div class="cart-item">
        <div class="flex-row ai-center">
            <div class="item-checkbox">
                <el-checkbox
                    v-model="item.selected"
                    @change="handleSelectionChange"
                />
            </div>

            <div class="item-image">
                <img :src="item.goodsMainImg" :alt="item.goodsName" />
            </div>
        </div>

        <div class="item-info">
            <div class="item-title">{{ item.goodsName }}</div>
            <p class="item-description">{{ item.cateName }}</p>
        </div>

        <div class="item-price">
            <div class="current-price">¥{{ item.buyPrice }}</div>
            <div class="original-price" v-if="item.oriPrice">
                ¥{{ item.oriPrice }}
            </div>
        </div>

        <div class="item-quantity">
            <el-input-number
                v-model="item.num"
                :min="1"
                :max="99"
                controls-position="right"
                size="small"
                @change="handleQuantityChange"
            />
        </div>

        <div class="item-actions">
            <el-button type="danger" text size="small" @click="handleRemove(item)">
                <svg-icon name="delete"></svg-icon>
                <span>删除</span>
            </el-button>
        </div>
    </div>
</template>

<script setup>
import SvgIcon from '@/components/SvgIcon/SvgIcon.vue'
import { useCartStore } from '@/stores/shop-cart.js'

const props = defineProps({
    item: {
        type: Object,
        required: true
    },
    tenantId: {
        type: String,
        required: true
    }
})

const cart = useCartStore()

// 动态修改身份选中
const handleSelectionChange = (selected) => {
    console.log('selected', selected)

    nextTick(() => {
        cart.$patch((state) => {
            let shopInfo = state.items.find((child) => child.tenantId === props.tenantId)
            shopInfo.children.forEach((child) => {
                if (child.id === props.item?.id) {
                    child.selected = selected
                }
            })
        })
    })
}

const handleQuantityChange = (quantity) => {
    nextTick(() => {
        cart.$patch((state) => {
            let shopInfo = state.items.find((child) => child.tenantId === props.tenantId)
            shopInfo.children.forEach((child) => {
                if (child.id === props.item?.id) {
                    child.num = quantity
                    // 调接口修改数量
                    cart.updateQuantity({
                        carId: child.id,
                        num: quantity
                    })
                }
            })
        })
    })
}

// 删除
const handleRemove = (item) => {
    let productIds = [item.id]
    cart.removeFromCart(productIds)
}
</script>

<style lang="scss" scoped>
.cart-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    background: white;
    margin-bottom: 12px;
    border-bottom: 1px solid #ededed;
    gap: 16px;

    &:last-child {
        margin-bottom: 0;
        border-bottom: none;
    }

    &:hover {
        background: rgba(64, 158, 255, 0.1);
    }

    .item-checkbox {
        margin-right: 10px;
    }

    .item-image {
        width: 100px;
        height: 100px;
        border-radius: 8px;
        overflow: hidden;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }

    .item-info {
        flex: 1;

        .item-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .item-description {
            font-size: 14px;
            color: #999;
            margin-bottom: 12px;
            line-height: 1.4;
        }
    }

    .item-price {
        width: 150px;
        text-align: center;

        .current-price {
            font-size: 18px;
            font-weight: 600;
            color: #e74c3c;
        }

        .original-price {
            font-size: 14px;
            color: #999;
            text-decoration: line-through;
            margin-top: 4px;
        }
    }

    .item-quantity {
        width: 150px;

        :deep(.el-input-number) {
            width: 100%;
        }
    }

    .item-actions {
        width: 80px;
        text-align: center;
    }
}

// 响应式设计
@media (max-width: 768px) {
    .cart-item {
        flex-direction: column;
        align-items: stretch;
        padding: 16px;

        .item-checkbox {
            margin-right: 0;
            margin-bottom: 12px;
            align-self: flex-start;
        }

        .item-image {
            width: 80px;
            height: 80px;
            margin-right: 12px;
            margin-bottom: 0;
            align-self: flex-start;
        }

        .item-info {
            margin-right: 0;
            margin-bottom: 12px;
        }

        .item-price,
        .item-quantity,
        .item-actions {
            width: auto;
            margin-right: 0;
            margin-bottom: 8px;
            text-align: left;
        }

        .item-quantity {
            :deep(.el-input-number) {
                width: 120px;
            }
        }
    }
}
</style>
