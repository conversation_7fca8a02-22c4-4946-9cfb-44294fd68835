<template>
    <div class="goods-item-grid" @click="handleGoodsClick(item)">
        <div class="goods-image">
            <el-image
                :src="item.mainImg || '/placeholder-image.jpg'"
                fit="cover">
                <template #error>
                    <div class="image-slot">
                        <el-icon>
                            <Picture></Picture>
                        </el-icon>
                    </div>
                </template>
            </el-image>
            <!-- 商品标签 -->
            <div class="goods-tags" v-if="item.isPriority || item.type === 3">
                <el-tag v-if="item.isPriority" type="danger" size="small">
                    优品
                </el-tag>
                <el-tag v-if="item.type === 3" type="warning" size="small">
                    拼团
                </el-tag>
            </div>
        </div>

        <div class="goods-info">
            <div class="goods-title text-over" :title="item.title">
                {{ item.title }}
            </div>
            <div class="price-section flex-row ai-center">
                <template v-if="item.type === 1">
                    <div class="current-price">¥{{ item.price }}</div>
                    <div class="original-price">
                        ¥{{ item.oriPrice }}
                    </div>
                </template>
                <div v-if="item.type === 3 && item.groupPrice" class="group-price">
                    拼团价：¥{{ item.groupPrice }}
                </div>
            </div>

            <div class="goods-stats">
                <div class="stat-item">销量: {{ item.sellNum || 0 }}</div>
                <div class="stat-item">库存: {{ item.stock || 0 }}</div>
            </div>

            <div class="company-info" v-if="item.companyInfo">
                <div class="company-name">{{ item.companyInfo.name }}</div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { Picture } from '@element-plus/icons-vue'
// 定义props
defineProps({
    item: {
        type: Object,
        required: false,
        default: () => ({})
    }
})

const emit = defineEmits(['itemClick'])

// 商品点击处理
const handleGoodsClick = (item) => {
    // TODO: 跳转到商品详情页
    emit('itemClick', item)
}
</script>

<style scoped lang="scss">
.goods-item-grid {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
        transform: translateY(-4px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    .goods-image {
        position: relative;
        height: 200px;
        overflow: hidden;

        .el-image {
            width: 100%;
            height: auto;
            object-fit: cover;
        }

        .image-slot {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            background: #f5f7fa;
            color: #909399;
            font-size: 30px;
        }

        .goods-tags {
            position: absolute;
            top: 8px;
            left: 8px;
            display: flex;
            gap: 4px;
        }
    }

    .goods-info {
        padding: 16px;

        .goods-title {
            font-size: 15px;
            font-weight: 600;
            color: #303133;
            margin: 0 0 8px 0;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .price-section {
            margin-bottom: 12px;

            .current-price {
                font-size: 18px;
                font-weight: 600;
                color: #e6a23c;
                margin-right: 8px;
            }

            .original-price {
                font-size: 14px;
                color: #909399;
                text-decoration: line-through;
                margin-right: 8px;
            }

            .group-price {
                font-size: 14px;
                color: #f56c6c;
                display: block;
            }
        }

        .goods-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;

            .stat-item {
                font-size: 12px;
                color: #909399;
            }
        }

        .company-info {
            .company-name {
                font-size: 12px;
                color: #606266;
            }
        }
    }
}
</style>
