<template>
    <div class="company-item">
        <!-- 左侧：公司图标 -->
        <div class="company-logo-section">
            <div class="company-logo">
                <el-image :src="company.logo"></el-image>
            </div>
        </div>

        <!-- 中间：公司信息 -->
        <div class="company-info-section">
            <!-- 公司名称和标签 -->
            <div class="company-header">
                <h3 class="company-name">{{ company.name }}</h3>
            </div>

            <!-- 详细信息 -->
            <div class="company-details">
                <div class="detail-row">
                    <span class="detail-label">法定代表人：</span>
                    <span class="detail-value">{{ company.legalPerson }}</span>
                    <span class="detail-separator">|</span>
                    <span class="detail-label">注册资本：</span>
                    <span class="detail-value">{{ company.registeredCapital || '未知' }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">成立日期：</span>
                    <span class="detail-value">{{ company.establishDate || '未知' }}</span>
                    <span class="detail-separator">|</span>
                    <span class="detail-label">统一社会信用代码：</span>
                    <span class="detail-value credit-code">
                        {{ company.uscc }}
                    </span>
                </div>
                <div v-if="company.contactNumber" class="detail-row">
                    <span class="detail-label">电话：</span>
                    <span class="detail-value">{{ company.contactNumber }}</span>
                    <span class="detail-separator">|</span>
                    <span class="detail-label">邮箱：</span>
                    <span class="detail-value">{{ company.email || '暂无' }}</span>
                </div>
                <div v-if="company.address" class="detail-row address-row">
                    <span class="detail-label">地址：</span>
                    <span class="detail-value">{{ company.address }}</span>
                </div>
            </div>
        </div>

        <!-- 右侧：操作按钮 -->
        <div class="action-section">
            <button class="action-btn detail-btn" @click="handleDetail(company)">查看详情</button>
        </div>
    </div>
</template>

<script setup>
// 定义props
defineProps({
    company: {
        type: Object,
        required: false,
        default: () => ({}),
    },
})

const emits = defineEmits(['item'])

const handleDetail = (item) => {
    emits('item', item)
}
</script>

<style scoped lang="scss">
.company-item {
    background: white;
    display: flex;
    align-items: center;
    padding: 20px;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #f0f0f0;

    &:hover {
        background-color: #f8f9fa;
    }

    &:last-child {
        border-bottom: none;
    }

    /* 左侧：公司图标 */
    .company-logo-section {
        flex-shrink: 0;
        margin-right: 20px;

        .company-logo {
            width: 80px;
            height: 80px;

            .el-image {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 5px;
            }
        }
    }

    /* 中间：公司信息 */
    .company-info-section {
        flex: 1;
        min-width: 0;

        .company-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 8px;

            .company-name {
                font-size: 18px;
                font-weight: 600;
                color: #333;
                margin: 0;
                text-decoration: none;
                cursor: pointer;

                &:hover {
                    text-decoration: underline;
                }
            }
        }

        .company-details {
            display: flex;
            flex-direction: column;
            gap: 4px;

            .detail-row {
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 14px;
                line-height: 1.4;
                flex-wrap: wrap;

                &.address-row {
                    margin-top: 4px;
                }

                .detail-label {
                    color: #666;
                    font-weight: 500;
                }

                .detail-value {
                    color: #333;

                    &.credit-code {
                        font-family: 'Courier New', monospace;
                        font-size: 13px;
                        background-color: #f8f9fa;
                        padding: 2px 6px;
                        border-radius: 3px;
                    }
                }

                .detail-separator {
                    color: #ccc;
                    margin: 0 4px;
                }
            }
        }
    }

    /* 右侧：操作按钮 */
    .action-section {
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-left: 20px;

        .action-btn {
            padding: 8px 16px;
            border: 1px solid #dadce0;
            border-radius: 4px;
            background: white;
            color: #1a73e8;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: center;
            min-width: 80px;

            &:hover {
                background-color: #f8f9fa;
                border-color: #1a73e8;
            }

            &.follow-btn {
                background-color: #1a73e8;
                color: white;
                border-color: #1a73e8;

                &:hover {
                    background-color: #1557b0;
                }
            }

            &.detail-btn {
                color: #5f6368;
                border-color: #dadce0;

                &:hover {
                    color: #1a73e8;
                    border-color: #1a73e8;
                }
            }
        }
    }
}
</style>
