<template>
    <el-dialog
        v-model="dialogVisible"
        :destroy-on-close="true"
        :title="formData.id ? '编辑地址' : '添加地址'"
        width="500px"
        :before-close="handleClose"
    >
        <el-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="80px"
            label-position="left"
        >
            <el-form-item label="收件人" prop="realname">
                <el-input
                    v-model="formData.realname"
                    placeholder="请输入收件人姓名"
                    maxlength="20"
                />
            </el-form-item>

            <el-form-item label="手机号" prop="phone">
                <el-input v-model="formData.phone" placeholder="请输入手机号" maxlength="11" />
            </el-form-item>

            <el-form-item label="省市区" prop="province">
                <el-cascader
                    v-model="areaValue"
                    :options="areaList"
                    :props="areaProps"
                    placeholder="请选择省市区"
                    @change="handleAreaChange"
                ></el-cascader>
            </el-form-item>

            <el-form-item label="详细地址" prop="address">
                <el-input
                    v-model="formData.address"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入详细地址，如街道、门牌号等"
                    maxlength="100"
                    show-word-limit
                />
            </el-form-item>

            <el-form-item>
                <el-checkbox v-model="isDefault" @change="handleDefault"> 设为默认地址</el-checkbox>
            </el-form-item>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" :loading="saving" @click="handleSave"> 保存</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { useOtherStore } from '@/stores/other.js'
import { addAddress } from '@/api/address.js'

const props = defineProps({
    address: {
        type: Object,
        default: null,
    },
})

const emit = defineEmits(['visible', 'save'])
const otherStore = useOtherStore()

const areaValue = ref([])
const areaList = ref(otherStore.regionList)
const areaProps = reactive({
    label: 'name',
    value: 'name',
    children: 'children',
})
const isDefault = ref(false)
// 表单引用
const formRef = ref()
// 保存状态
const saving = ref(false)
const dialogVisible = ref(false)

// 表单数据
const formData = ref({
    id: null,
    realname: '', // 姓名
    gender: '', // 1男 2女
    phone: '', // 手机
    provinceCode: '', // 省代码
    cityCode: '', // 市代码
    areaCode: '', // 区代码
    province: '', // 省
    city: '', // 市
    area: '', // 区
    address: '', // 收货地址
    lng: '', // x坐标
    lat: '', // y坐标
    target: '', // 标签
    status: 0, // 默认状态(0:未默认 1:默认)
})

const handleAreaChange = (value) => {
    console.log('省市区', value)
    if (value[0]) {
        formData.value.province = value[0]
    }

    if (value[1]) {
        formData.value.city = value[1]
    }

    if (value[2]) {
        formData.value.area = value[2]
    }
}

// 设置默认
const handleDefault = (ev) => {
    formData.value.status = ev ? 1 : 0
}

// 表单验证规则
const formRules = {
    realname: [
        { required: true, message: '请输入收件人姓名', trigger: 'blur' },
        { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' },
    ],
    phone: [
        { required: true, message: '请输入手机号', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
    ],
    address: [
        { required: true, message: '请输入详细地址', trigger: 'blur' },
        { min: 5, max: 100, message: '详细地址长度在 5 到 100 个字符', trigger: 'blur' },
    ],
    province: [{ required: true, message: '请选择省市区', trigger: 'change' }],
}

const openDialog = (info) => {
    dialogVisible.value = true
    if (info) {
        formData.value = { ...info }
        areaValue.value = [info.province, info.city, info.area]
        isDefault.value = info.status === 1
    }
}

// 保存地址
const handleSave = async () => {
    if (!formRef.value) return

    try {
        await formRef.value.validate()
        saving.value = true

        // 模拟保存过程
        await addAddress(formData.value)

        emit('save', { ...formData.value })
        handleClose()
    } catch (error) {
        console.log('表单验证失败:', error)
    } finally {
        saving.value = false
    }
}

// 关闭对话框
const handleClose = () => {
    dialogVisible.value = false
    resetForm()
}

// 重置表单
const resetForm = () => {
    if (formRef.value) {
        formRef.value.resetFields()
    }

    formData.value = {
        id: null,
        realname: '', // 姓名
        gender: '', // 1男 2女
        phone: '', // 手机
        provinceCode: '', // 省代码
        cityCode: '', // 市代码
        areaCode: '', // 区代码
        province: '', // 省
        city: '', // 市
        area: '', // 区
        address: '', // 收货地址
        lng: '', // x坐标
        lat: '', // y坐标
        target: '', // 标签
        status: 0, // 默认状态(0:未默认 1:默认)
    }
    isDefault.value = false
    areaValue.value = []
}

defineExpose({
    openDialog,
})
</script>

<style lang="scss" scoped>
.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

:deep(.el-form-item__label) {
    font-weight: 500;
}
</style>
