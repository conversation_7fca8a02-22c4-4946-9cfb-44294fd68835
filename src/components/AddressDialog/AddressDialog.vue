<template>
    <el-dialog
        v-model="dialogVisible"
        :destroy-on-close="true"
        title="选择收货地址"
        width="600px"
        :before-close="handleClose"
    >
        <div class="address-dialog">
            <!-- 地址列表 -->
            <div class="address-list">
                <div
                    v-for="address in addresses"
                    :key="address.id"
                    class="address-item"
                    :class="{ active: selectedAddressId === address.id }"
                    @click="selectAddress(address)"
                >
                    <div class="address-radio">
                        <el-radio
                            :model-value="selectedAddressId"
                            :value="address.id"
                            @change="selectAddress(address)"
                        />
                    </div>

                    <div class="address-content">
                        <div class="address-header flex-row ai-center">
                            <span class="recipient">{{ address.realname }}</span>
                            <span class="phone">{{ address.phone }}</span>
                            <el-tag v-if="address.status === 1" type="primary" size="small">
                                默认
                            </el-tag>
                        </div>
                        <div class="address-detail">
                            {{ address.province }} {{ address.city }} {{ address.area }}
                            {{ address.address }}
                        </div>
                    </div>

                    <div class="address-actions flex-row ai-center jc-flex-end">
                        <div
                            v-if="address.status !== 1"
                            class="_btn _del"
                            @click.stop="deleteAddr(address.id)"
                        >
                            删除
                        </div>
                        <div class="_btn" @click.stop="editAddress(address)">编辑</div>
                    </div>
                </div>

                <!-- 空状态 -->
                <div v-if="addresses.length === 0" class="empty-address">
                    <svg-icon name="location" color="#ccc" size="32"></svg-icon>
                    <p>暂无收货地址</p>
                </div>
            </div>

            <!-- 添加新地址按钮 -->
            <div class="add-address">
                <el-button type="primary" @click="editAddress()">
                    <svg-icon name="plus" color="#fff"></svg-icon>
                    添加新地址
                </el-button>
            </div>
        </div>

        <template #footer>
            <div class="dialog-footer flex-row jc-flex-end">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="confirmSelection" :disabled="!selectedAddressId">
                    确认选择
                </el-button>
            </div>
        </template>
    </el-dialog>

    <!-- 地址表单对话框 -->
    <address-form ref="addressFormRef" @save="handleSaveAddress"></address-form>
</template>

<script setup>
import { ElMessageBox } from 'element-plus'
import SvgIcon from '@/components/SvgIcon/SvgIcon.vue'
import AddressForm from './AddressForm.vue'
import { deleteAddress, getAddressList } from '@/api/address.js'
import { useMessage } from '@/utils/useMessage.js'

const props = defineProps({
    visible: {
        type: Boolean,
        default: false,
    },
    selectedAddress: {
        type: Object,
        default: null,
    },
})

const emit = defineEmits(['update:visible', 'update:selectedAddress'])
// 地址列表
const addresses = ref([])
// 选中的地址ID
const selectedAddressId = ref(props.selectedAddress?.id || null)

// 地址表单
const addressFormRef = ref(null)

// 对话框显示状态
const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
})

watch(
    dialogVisible,
    (newVal) => {
        if (newVal) {
            loadAddressList()
        }
    },
    {
        deep: true,
        immediate: true,
    },
)

// 监听选中地址变化
watch(
    () => props.selectedAddress,
    (newAddress) => {
        selectedAddressId.value = newAddress?.id || null
    },
)

const loadAddressList = async () => {
    try {
        // 写死不要分页
        const response = await getAddressList({ current: 1, size: 100 })
        console.log('地址列表', response)
        addresses.value = response.data['records'] || []
    } catch (error) {
        console.error('获取地址列表失败:', error)
    }
}

// 选择地址
const selectAddress = (address) => {
    selectedAddressId.value = address.id
    confirmSelection()
}

// 编辑地址
const editAddress = (address) => {
    addressFormRef.value.openDialog(address)
}

// 删除地址
const deleteAddr = (addressId) => {
    try {
        ElMessageBox.confirm('确定要删除这个地址吗？', '确认删除', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })
            .then(async () => {
                let params = {
                    ids: [addressId],
                }
                const response = await deleteAddress(params)
                console.log('删除地址结果', response)
                loadAddressList()
                useMessage().success('删除成功')
            })
            .catch(() => {})
    } catch {
        // 用户取消删除
    }
}

// 保存地址
const handleSaveAddress = () => {
    loadAddressList()
}

// 关闭对话框
const handleClose = () => {
    emit('update:visible', false)
}

// 确认选择
const confirmSelection = () => {
    const selectedAddr = addresses.value.find((addr) => addr.id === selectedAddressId.value)
    if (selectedAddr) {
        emit('update:selectedAddress', selectedAddr)
    }
    handleClose()
}
</script>

<style lang="scss" scoped>
.address-dialog {
    .address-list {
        max-height: 400px;
        overflow-y: auto;
        margin-bottom: 16px;

        .address-item {
            display: flex;
            align-items: flex-start;
            padding: 16px;
            border: 2px solid #f0f0f0;
            border-radius: 8px;
            margin-bottom: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            gap: 10px;

            &:hover {
                border-color: #409eff;
                background-color: #f8fbff;
            }

            &.active {
                border-color: #409eff;
                background-color: #f0f9ff;
            }

            .address-radio {
                margin-top: -5px;
            }

            .address-content {
                flex: 1;

                .address-header {
                    gap: 12px;
                    margin-bottom: 8px;

                    .recipient {
                        font-weight: 600;
                        color: #333;
                    }

                    .phone {
                        color: #666;
                    }
                }

                .address-detail {
                    color: #666;
                    line-height: 1.5;
                }
            }

            .address-actions {
                width: 100px;
                height: 100%;
                gap: 10px;

                ._btn {
                    font-size: 12px;
                    color: #3d8af5;

                    &:hover {
                        color: #1a73e8;
                    }

                    &._del {
                        color: #f56c6c;

                        &:hover {
                            color: #c45656;
                        }
                    }
                }
            }
        }

        .empty-address {
            text-align: center;
            padding: 40px 20px;
            color: #999;

            p {
                margin-top: 12px;
                font-size: 16px;
            }
        }
    }

    .add-address {
        text-align: center;
        padding: 16px 0;
        border-top: 1px solid #f0f0f0;
    }
}

.dialog-footer {
    gap: 12px;
}
</style>
