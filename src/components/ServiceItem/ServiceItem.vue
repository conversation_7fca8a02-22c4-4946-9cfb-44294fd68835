<template>
    <div class="service-card" @click="handleServiceClick(service)">
        <!-- 服务图片/图标 -->
        <div class="service-image">
            <img :src="service.mainImg" :alt="service.title" />
            <!-- 角标 -->
            <div v-if="service.badge" class="service-badge">
                {{ service.badge }}
            </div>
        </div>

        <!-- 服务内容 -->
        <div class="service-content">
            <!-- 价格 -->
            <div class="service-price">
                <span v-if="service.saleType === 1" class="price-symbol">¥</span>
                <span class="price-amount">{{ service.showPrice }}</span>
            </div>

            <!-- 标题 -->
            <h3 class="service-title">{{ service.title }}</h3>

            <!-- 描述 -->
            <div class="service-description flex-row ai-center">
                <div class="__item">
                    {{ service.pointNum }}
                    浏览
                </div>
                <div class="__item">
                    {{ service.collectionNum }}
                    收藏
                </div>
            </div>

            <!-- 评分和销量 -->
            <div class="service-stats">
                <div class="cate">
                    {{ service.cateName }}
                </div>
                <div class="sales">
                    <span class="sales-count">{{ service.sellNum }}</span>
                    <span class="sales-text">已售 {{ service.soldCount }}</span>
                </div>
            </div>

            <div class="service-name flex-row ai-center">
                <el-image :src="service.companyInfo.logo"></el-image>
                <div class="_label">
                    {{ service.companyInfo.name }}
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="service-actions" v-if="false">
                <el-button
                    type="warning"
                    size="small"
                    class="consult-btn"
                    @click="handleConsult(service)"
                >
                    <svg-icon name="zixun"></svg-icon>
                    咨询
                </el-button>
                <el-button
                    type="success"
                    size="small"
                    class="wechat-btn"
                    @click="handleWechatConsult(service)"
                >
                    <svg-icon name="caigouren"></svg-icon>
                    立即购买
                </el-button>
            </div>
        </div>
    </div>
</template>

<script setup>
import SvgIcon from '@/components/SvgIcon/SvgIcon.vue'

defineProps({
    service: {
        type: Object,
        required: false,
        default: () => ({}),
    },
})

const emit = defineEmits(['itemClick'])

// 事件处理
const handleConsult = (service) => {
    console.log('咨询服务:', service.title)
    // 这里可以添加咨询逻辑
}

const handleWechatConsult = (service) => {
    console.log('微信咨询服务:', service.title)
    // 这里可以添加微信咨询逻辑
}

const handleServiceClick = (item) => {
    // TODO: 跳转到商品详情页
    emit('itemClick', item)
}
</script>

<style scoped lang="scss">
.service-card {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition:
        transform 0.3s ease,
        box-shadow 0.3s ease;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    .service-image {
        position: relative;
        height: 150px;
        overflow: hidden;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .service-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
    }

    .service-content {
        padding: 16px;

        .service-price {
            margin-bottom: 8px;

            .price-symbol {
                color: #ff6b6b;
                font-size: 16px;
                font-weight: bold;
            }

            .price-amount {
                color: #ff6b6b;
                font-size: 24px;
                font-weight: bold;
            }
        }

        .service-title {
            font-size: 15px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .service-description {
            width: 100%;
            color: #666;
            font-size: 12px;
            line-height: 1.4;
            margin-bottom: 12px;
            gap: 20px;
        }

        .service-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;

            .cate {
                font-size: 12px;
                background: #3d8af5;
                color: #fff;
                padding: 2px 10px;
                border-radius: 5px;
            }

            .sales {
                font-size: 12px;
                color: #999;

                .sales-count {
                    color: #666;
                    margin-right: 4px;
                }
            }
        }

        .service-name {
            gap: 5px;

            .el-image {
                height: 20px;
                object-fit: contain;
                border-radius: 4px;
            }

            ._label {
                flex: 1;
                font-size: 12px;
                color: #626066;
            }
        }

        .service-actions {
            display: flex;
            gap: 8px;

            .consult-btn {
                flex: 1;
                background: #ff9500;
                border-color: #ff9500;

                &:hover {
                    background: #e6850e;
                    border-color: #e6850e;
                }
            }

            .wechat-btn {
                flex: 1;
                background: #07c160;
                border-color: #07c160;

                &:hover {
                    background: #06ad56;
                    border-color: #06ad56;
                }
            }
        }
    }
}
</style>
