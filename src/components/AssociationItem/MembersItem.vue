<template>
    <div class="company-item">
        <!-- 左侧：公司图标 -->
        <div class="company-logo-section">
            <div class="company-logo">
                <el-image :src="company.companyLogo"></el-image>
            </div>
        </div>

        <!-- 中间：公司信息 -->
        <div class="company-info-section">
            <!-- 公司名称和标签 -->
            <div class="company-header">
                <h3 class="company-name">{{ company.companyName }}</h3>
            </div>

            <!-- 详细信息 -->
            <div class="company-details">
                <div class="detail-row address-row">
                    <span class="detail-value">{{ company.categoryName }}</span>
                </div>
            </div>
        </div>

        <!-- 右侧：操作按钮 -->
        <div class="action-section">
            <button class="action-btn detail-btn" @click="handleDetail(company)">查看详情</button>
        </div>
    </div>
</template>

<script setup>
// 定义props
defineProps({
    company: {
        type: Object,
        required: false,
        default: () => ({}),
    },
})

const emits = defineEmits(['item'])

const handleDetail = (item) => {
    emits('item', item)
}
</script>

<style scoped lang="scss">
.company-item {
    background: white;
    display: flex;
    align-items: center;
    padding: 20px;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #f0f0f0;

    &:hover {
        background-color: #f8f9fa;
    }

    &:last-child {
        border-bottom: none;
    }

    /* 左侧：公司图标 */
    .company-logo-section {
        flex-shrink: 0;
        margin-right: 20px;

        .company-logo {
            width: 80px;
            height: 80px;

            .el-image {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 5px;
            }
        }
    }

    /* 中间：公司信息 */
    .company-info-section {
        flex: 1;
        min-width: 0;

        .company-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 8px;

            .company-name {
                font-size: 18px;
                font-weight: 600;
                color: #333;
                margin: 0;
                text-decoration: none;
                cursor: pointer;

                &:hover {
                    text-decoration: underline;
                }
            }
        }

        .company-details {
            display: flex;
            flex-direction: column;
            gap: 4px;

            .detail-row {
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 14px;
                line-height: 1.4;
                flex-wrap: wrap;

                &.address-row {
                    margin-top: 4px;
                }

                .detail-label {
                    color: #666;
                    font-weight: 500;
                }

                .detail-value {
                    color: #333;

                    &.credit-code {
                        font-family: 'Courier New', monospace;
                        font-size: 13px;
                        background-color: #f8f9fa;
                        padding: 2px 6px;
                        border-radius: 3px;
                    }
                }
            }
        }
    }

    /* 右侧：操作按钮 */
    .action-section {
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-left: 20px;

        .action-btn {
            padding: 8px 16px;
            border: 1px solid #dadce0;
            border-radius: 4px;
            background: white;
            color: #1a73e8;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: center;
            min-width: 80px;

            &:hover {
                background-color: #f8f9fa;
                border-color: #1a73e8;
            }

            &.detail-btn {
                color: #5f6368;
                border-color: #dadce0;

                &:hover {
                    color: #1a73e8;
                    border-color: #1a73e8;
                }
            }
        }
    }
}
</style>
