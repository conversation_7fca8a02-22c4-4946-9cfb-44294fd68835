<template>
    <el-drawer
        v-model="innerDrawer"
        title="确认订单"
        :append-to-body="true"
        size="60%"
        @close="closeDrawer"
    >
        <el-scrollbar class="hide-scrollbar">
            <!--  地址  -->
            <div class="payment-content">
                <div class="address-section">
                    <div
                        class="address-card flex-row jc-space-between ai-center"
                        :class="{ 'no-address': !selectedAddress }"
                    >
                        <div v-if="selectedAddress" class="address-info">
                            <div class="address-header flex-row ai-center">
                                <span class="recipient">{{ selectedAddress.realname }}</span>
                                <span class="phone">{{ selectedAddress.phone }}</span>
                                <el-tag
                                    v-if="selectedAddress.status === 1"
                                    type="primary"
                                    size="small"
                                >
                                    默认
                                </el-tag>
                            </div>
                            <div class="address-detail">
                                {{ selectedAddress.province }} {{ selectedAddress.city }}
                                {{ selectedAddress.area }} {{ selectedAddress.address }}
                            </div>
                        </div>
                        <div v-else class="no-address-info flex-row ai-center">
                            <svg-icon name="location" color="#ccc"></svg-icon>
                            <span>请添加收货地址</span>
                        </div>
                        <el-button type="primary" text @click="openAddRess">
                            {{ selectedAddress ? '更换地址' : '添加地址' }}
                        </el-button>
                    </div>
                </div>
            </div>

            <div class="good-list">
                <template v-for="item in dataList" :key="item.shopId">
                    <el-card class="good-card">
                        <template #header>
                            <div class="_header flex-row ai-center">
                                <el-image class="_logo" :src="item.companyInfoVo.logo"></el-image>
                                <div class="_label">{{ item.companyInfoVo.name }}</div>
                            </div>
                        </template>

                        <template v-for="(child, childIndex) in item.goodsList" :key="childIndex">
                            <div class="good-item flex-row ai-center">
                                <div class="_item item-image">
                                    <el-image
                                        class="_mainImg"
                                        :src="child.goods.mainImg"
                                    ></el-image>
                                </div>
                                <div class="_item flex-column item-info">
                                    <div class="_title">{{ child.goods.title }}</div>
                                    <div class="_cate">{{ child.goods.cateName }}</div>
                                </div>
                                <div class="_item item-price">
                                    <div class="current-price">¥{{ child.goods.price }}</div>
                                    <div class="original-num">x {{ child.buyNum }}</div>
                                </div>
                            </div>
                        </template>
                    </el-card>
                </template>
            </div>
        </el-scrollbar>
        <!-- 地址选择对话框 -->
        <address-dialog
            v-model:visible="showAddressDialog"
            v-model:selected-address="selectedAddress"
        >
        </address-dialog>

        <template #footer>
            <div class="_footer flex-row jc-flex-end ai-center">
                <div class="_left flex-row ai-center">
                    <div class="_txt">共{{ totalNum }}件商品</div>
                    <div class="_txt">合计:</div>
                    <div class="_price">¥{{ totalPrice }}</div>
                </div>
                <div class="_right">
                    <el-button type="primary" @click="submitOrder">提交订单</el-button>
                </div>
            </div>
        </template>

        <!--  二维码弹窗  -->
        <qr-code-display
            ref="qrcodeRef"
            :pay-order-no="payOrderNo"
            @pay-over="handlePayOver"
        ></qr-code-display>
    </el-drawer>
</template>

<script setup>
import { addOrder, orderPay, orderPlace } from '@/api/order.js'
import SvgIcon from '@/components/SvgIcon/SvgIcon.vue'
import AddressDialog from '@/components/AddressDialog/AddressDialog.vue'
import { useMessage } from '@/utils/useMessage.js'
import { getDefaultAddress } from '@/api/address.js'
import QrCodeDisplay from '@/components/QrCodeDisplay/QrCodeDisplay.vue'

const innerDrawer = ref(false)
const dataList = ref([])

// 二维码弹窗
const qrcodeRef = ref()
const payOrderNo = ref('')

// 收货地址
const selectedAddress = ref(null)
const showAddressDialog = ref(false)
const placeOrderParams = reactive({})

const totalNum = ref(0)
const totalPrice = ref(0)

const openAddRess = () => {
    nextTick(() => {
        showAddressDialog.value = true
    })
}

// 用户选择的地址ID
const userAddressId = computed(() => {
    return selectedAddress.value.id
})

const openDrawer = async (params) => {
    console.log('确认订单参数', params)
    Object.assign(placeOrderParams, params)

    // 尝试着加载有没有默认地址
    let { data } = await getDefaultAddress()
    console.log('默认地址——data', data)

    if (data) {
        selectedAddress.value = data
    }

    await loadOrderPlace(placeOrderParams)
    innerDrawer.value = true
}

const closeDrawer = () => {
    innerDrawer.value = false
}

const loadOrderPlace = async (params) => {
    try {
        // let { data } = await orderPlace(params)
        let result = await orderPlace(params)
        console.log('结算单结果', result)
        dataList.value = result.data

        if (result.data) {
            const total = calculateOrderSummary(dataList.value)

            totalNum.value = total.totalNum
            totalPrice.value = total.totalPrice
        } else {
            console.log(1)
            closeDrawer()
        }
    } catch (e) {
        console.log('发生了错误', e)
    }
}

const calculateOrderSummary = (orderData) => {
    let totalQuantity = 0
    let totalPrice = 0

    orderData.forEach((shop) => {
        shop.goodsList.forEach((item) => {
            totalQuantity += item.buyNum
            totalPrice += item.buyNum * item.goods.price
        })
    })

    // 四舍五入保留2位小数（处理浮点数精度问题）
    totalPrice = Math.round(totalPrice * 100) / 100

    return {
        totalNum: totalQuantity,
        totalPrice: totalPrice.toFixed(2), // 确保始终显示2位小数
    }
}

// 添加订单(生成订单)
const submitOrder = async () => {
    try {
        console.log('dataList.value', dataList.value)
        console.log('userAddressId', userAddressId.value)

        let addFormList = []

        // 生成订单的参数
        let orderParams = {
            addFormList: [],
            userAddressId: userAddressId.value,
            addMode: 1, // 下单平台 1PC 2小程序
        }

        dataList.value.forEach((n) => {
            let item = {
                remark: 'PC端下单',
                goodsDetail: [],
                userAddressId: userAddressId.value,
                carIds: placeOrderParams.goodsList.map((n) => n.carId),
            }

            n.goodsList.forEach((nn) => {
                item.goodsDetail.push({
                    goodsId: nn.goods.id,
                    num: nn.buyNum,
                    shopId: n.shopId,
                })
            })

            addFormList.push(item)
        })

        orderParams.addFormList = addFormList

        console.log('添加订单提交的参数', orderParams)

        const { data, code } = await addOrder(orderParams)

        if (code === 0) {
            useMessage().success('订单生成成功')

            let payParams = {
                payOrderNo: data.payOrderNo,
                payMethod: 'wxPay',
                payMode: 2,
            }

            // 查询订单支付状态使用的
            payOrderNo.value = data.payOrderNo

            const payData = await orderPay(payParams)
            qrcodeRef.value.openDialog(payData.data.message)
        }
    } catch (e) {
        console.log('生成订单发生了错误', e)
    }
}

const handlePayOver = (data) => {
    console.log('支付状态', data)
    // 	0待支付>>1支付成功>>2支付失败
    if (data.status === 1) {
        useMessage().success('支付成功')
    } else if (data.status === 0) {
        useMessage().warning('支付未完成')
    } else if (data.status === 1) {
        useMessage().error('支付失败')
    }

    closeDrawer()
}

defineExpose({
    openDrawer,
})
</script>

<style scoped lang="scss">
.payment-content {
    position: sticky;
    top: 0;
    z-index: 2;
    margin-bottom: 10px;

    .address-section {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

        .address-card {
            padding: 16px;
            border: 2px solid #f0f0f0;
            border-radius: 8px;

            &.no-address {
                border-style: dashed;
                border-color: #ddd;
            }

            .address-info {
                flex: 1;

                .address-header {
                    gap: 12px;
                    margin-bottom: 8px;

                    .recipient {
                        font-weight: 600;
                        color: #333;
                    }

                    .phone {
                        color: #666;
                    }
                }

                .address-detail {
                    color: #666;
                    line-height: 1.5;
                }
            }

            .no-address-info {
                gap: 5px;
                color: #999;
                flex: 1;
            }
        }
    }
}

.good-list {
    .good-card {
        margin-bottom: 10px;

        :deep(.el-card__header) {
            padding-top: 3px;
            padding-bottom: 3px;
        }

        ._header {
            gap: 10px;

            ._logo {
                height: 30px;
                object-fit: contain;
                border-radius: 5px;
            }

            ._label {
                font-size: 14px;
                color: #000;
                font-weight: 550;
            }
        }

        .good-item {
            gap: 10px;
            padding: 10px 0;
            border-bottom: 1px solid #ededed;

            &:last-child {
                border-bottom: none;
            }

            ._item {
                ._mainImg {
                    height: 100px;
                    border-radius: 5px;
                }

                ._title {
                    font-size: 14px;
                    font-weight: 600;
                    color: #333;
                    margin-bottom: 8px;
                    line-height: 1.4;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                }

                ._cate {
                    font-size: 14px;
                    color: #999;
                    margin-bottom: 12px;
                    line-height: 1.4;
                }

                &.item-image {
                    width: 100px;
                    height: 100px;
                }

                &.item-info {
                    flex: 1;
                }

                &.item-price {
                    width: 150px;
                    text-align: center;

                    .current-price {
                        font-size: 18px;
                        font-weight: 600;
                        color: #e74c3c;
                    }

                    .original-num {
                        font-size: 14px;
                        color: #999;
                        //text-decoration: line-through;
                        margin-top: 4px;
                    }
                }
            }
        }
    }
}

._footer {
    gap: 20px;

    ._left {
        gap: 10px;

        ._price {
            color: #e74c3c;
            font-weight: 550;
        }
    }
}
</style>
