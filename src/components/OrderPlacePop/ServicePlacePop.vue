<template>
    <el-drawer v-model="innerDrawer" title="确认订单" size="60%" @close="closeDrawer">
        <el-scrollbar class="hide-scrollbar">
            <!--  地址  -->
            <div class="payment-content">
                <div class="address-section">
                    <div
                        class="address-card flex-row jc-space-between ai-center"
                        :class="{ 'no-address': !selectedAddress }"
                    >
                        <div v-if="selectedAddress" class="address-info">
                            <div class="address-header flex-row ai-center">
                                <span class="recipient">{{ selectedAddress.realname }}</span>
                                <span class="phone">{{ selectedAddress.phone }}</span>
                                <el-tag
                                    v-if="selectedAddress.status === 1"
                                    type="primary"
                                    size="small"
                                >
                                    默认
                                </el-tag>
                            </div>
                            <div class="address-detail">
                                {{ selectedAddress.province }} {{ selectedAddress.city }}
                                {{ selectedAddress.area }} {{ selectedAddress.address }}
                            </div>
                        </div>
                        <div v-else class="no-address-info flex-row ai-center">
                            <svg-icon name="location" color="#ccc"></svg-icon>
                            <span>请添加收货地址</span>
                        </div>
                        <el-button type="primary" text @click="openAddRess">
                            {{ selectedAddress ? '更换地址' : '添加地址' }}
                        </el-button>
                    </div>
                </div>
            </div>

            <div class="good-list">
                <template v-for="item in dataList" :key="item.shopId">
                    <el-card class="good-card">
                        <template #header>
                            <div class="_header flex-row ai-center">
                                <el-image class="_logo" :src="item.companyInfo.logo"></el-image>
                                <div class="_label">{{ item.companyInfo.name }}</div>
                            </div>
                        </template>

                        <div class="good-item flex-row ai-center">
                            <div class="_item item-image">
                                <el-image class="_mainImg" :src="item.mainImg"></el-image>
                            </div>
                            <div class="_item flex-column item-info">
                                <div class="_title">{{ item.title }}</div>
                                <div class="_cate">{{ item.cateName }}</div>
                            </div>
                            <div class="_item item-price">
                                <div class="current-price">¥{{ item.showPrice }}</div>
                            </div>
                            <div class="_item item-price">
                                <div class="original-num">
                                    <el-input-number
                                        v-model="item.buyNum"
                                        :min="1"
                                        :max="100"
                                    ></el-input-number>
                                </div>
                            </div>
                        </div>
                    </el-card>

                    <el-card class="good-card">
                        <el-form v-model="formData" label-width="100px">
                            <el-form-item label="开始时间">
                                <el-date-picker
                                    v-model="formData.appointStartTime"
                                    value-format="YYYY-MM-DD HH:mm:ss"
                                    type="datetime"
                                    placeholder="服务预约开始日期"
                                ></el-date-picker>
                            </el-form-item>
                            <el-form-item label="结束时间">
                                <el-date-picker
                                    v-model="formData.appointEndTime"
                                    value-format="YYYY-MM-DD HH:mm:ss"
                                    type="datetime"
                                    placeholder="服务预约结束日期"
                                ></el-date-picker>
                            </el-form-item>
                            <el-form-item label="订单备注">
                                <el-input v-model="formData.remark" type="textarea"></el-input>
                            </el-form-item>
                        </el-form>
                    </el-card>
                </template>
            </div>
        </el-scrollbar>
        <!-- 地址选择对话框 -->
        <address-dialog
            v-model:visible="showAddressDialog"
            v-model:selected-address="selectedAddress"
        >
        </address-dialog>

        <template #footer>
            <div class="_footer flex-row jc-flex-end ai-center">
                <div class="_left flex-row ai-center">
                    <div class="_txt">合计:</div>
                    <div class="_price">¥{{ totalPrice }}</div>
                </div>
                <div class="_right">
                    <el-button type="primary" @click="submitOrder">提交订单</el-button>
                </div>
            </div>
        </template>

        <!--  二维码弹窗  -->
        <qr-code-display
            ref="qrcodeRef"
            :pay-order-no="payOrderNo"
            @pay-over="handlePayOver"
        ></qr-code-display>
    </el-drawer>
</template>

<script setup>
import SvgIcon from '@/components/SvgIcon/SvgIcon.vue'
import AddressDialog from '@/components/AddressDialog/AddressDialog.vue'
import { useMessage } from '@/utils/useMessage.js'
import { getDefaultAddress } from '@/api/address.js'
import { serviceCreateOrder, servicePayOrder } from '@/api/order.js'
import QrCodeDisplay from '@/components/QrCodeDisplay/QrCodeDisplay.vue'
import { getCurrentDateTime, getDateAfterDays } from '@/utils/index.js'

const innerDrawer = ref(false)
const dataList = ref([])

// 二维码弹窗
const qrcodeRef = ref()
const payOrderNo = ref('')

// 收货地址
const selectedAddress = ref(null)
const showAddressDialog = ref(false)

const openAddRess = () => {
    nextTick(() => {
        showAddressDialog.value = true
    })
}

// 用户选择的地址ID
const userAddressId = computed(() => {
    return selectedAddress.value.id
})

const totalPrice = computed(() => {
    let num = 0
    dataList.value.forEach((n) => {
        num += n.buyNum * n.showPrice
    })
    return num
})

const formData = reactive({
    listingsId: 0,
    buyNum: 0,
    userAddressId: 0,
    appointStartTime: '',
    appointEndTime: '',
    remark: '',
})

const openDrawer = async (serviceInfo) => {
    console.log('即将购买的服务', serviceInfo)

    let item = {
        ...serviceInfo,
        buyNum: 1,
    }

    formData.appointStartTime = getCurrentDateTime()
    formData.appointEndTime = getDateAfterDays(formData.appointStartTime, 7)

    dataList.value = [item]

    // 尝试着加载有没有默认地址
    let { data } = await getDefaultAddress()
    console.log('默认地址——data', data)

    if (data) {
        selectedAddress.value = data
    }
    innerDrawer.value = true
}

const closeDrawer = () => {
    innerDrawer.value = false
}

// 添加订单(生成订单)
const submitOrder = async () => {
    try {
        formData.userAddressId = userAddressId.value
        formData.listingsId = dataList.value[0].id
        formData.buyNum = dataList.value[0].buyNum
        console.log('提交的参数', formData)

        if (!formData.userAddressId) {
            return useMessage().error('请选择地址')
        }

        const { data, code } = await serviceCreateOrder(formData)

        if (code === 0) {
            useMessage().success('生成服务订单成功')
            console.log('data', data)

            let params = {
                payOrderNo: data.payOrderNo,
                payMethod: 'wxPay',
                payMode: 2,
            }

            let payData = await servicePayOrder(params)
            console.log('payData', payData)

            // 查询订单支付状态使用的
            payOrderNo.value = data.payOrderNo

            qrcodeRef.value.openDialog(payData.data.payInfo)
        }
    } catch (e) {
        console.log('生成订单发生了错误', e)
    }
}

const handlePayOver = (data) => {
    console.log('支付状态', data)
    // 	0待支付>>1支付成功>>2支付失败
    if (data.status === 1) {
        useMessage().success('支付成功')
    } else if (data.status === 0) {
        useMessage().warning('支付未完成')
    } else if (data.status === 1) {
        useMessage().error('支付失败')
    }

    closeDrawer()
}

defineExpose({
    openDrawer,
})
</script>

<style scoped lang="scss">
.payment-content {
    position: sticky;
    top: 0;
    z-index: 2;
    margin-bottom: 10px;

    .address-section {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

        .address-card {
            padding: 16px;
            border: 2px solid #f0f0f0;
            border-radius: 8px;

            &.no-address {
                border-style: dashed;
                border-color: #ddd;
            }

            .address-info {
                flex: 1;

                .address-header {
                    gap: 12px;
                    margin-bottom: 8px;

                    .recipient {
                        font-weight: 600;
                        color: #333;
                    }

                    .phone {
                        color: #666;
                    }
                }

                .address-detail {
                    color: #666;
                    line-height: 1.5;
                }
            }

            .no-address-info {
                gap: 5px;
                color: #999;
                flex: 1;
            }
        }
    }
}

.good-list {
    .good-card {
        margin-bottom: 10px;

        :deep(.el-card__header) {
            padding-top: 3px;
            padding-bottom: 3px;
        }

        ._header {
            gap: 10px;

            ._logo {
                height: 30px;
                object-fit: contain;
                border-radius: 5px;
            }

            ._label {
                font-size: 14px;
                color: #000;
                font-weight: 550;
            }
        }

        .good-item {
            gap: 10px;
            padding: 10px 0;
            border-bottom: 1px solid #ededed;

            &:last-child {
                border-bottom: none;
            }

            ._item {
                ._mainImg {
                    height: 100px;
                    border-radius: 5px;
                }

                ._title {
                    font-size: 14px;
                    font-weight: 600;
                    color: #333;
                    margin-bottom: 8px;
                    line-height: 1.4;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                }

                ._cate {
                    font-size: 14px;
                    color: #999;
                    margin-bottom: 12px;
                    line-height: 1.4;
                }

                &.item-image {
                    width: 100px;
                    height: 100px;
                }

                &.item-info {
                    flex: 1;
                }

                &.item-price {
                    width: 150px;
                    text-align: center;

                    .current-price {
                        font-size: 18px;
                        font-weight: 600;
                        color: #e74c3c;
                    }

                    .original-num {
                        font-size: 14px;
                        color: #999;
                        //text-decoration: line-through;
                        margin-top: 4px;
                    }
                }
            }
        }
    }
}

._footer {
    gap: 20px;

    ._left {
        gap: 10px;

        ._price {
            color: #e74c3c;
            font-weight: 550;
        }
    }
}
</style>
