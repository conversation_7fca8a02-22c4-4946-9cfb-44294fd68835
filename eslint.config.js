import { defineConfig, globalIgnores } from 'eslint/config'
import globals from 'globals'
import js from '@eslint/js'
import pluginVue from 'eslint-plugin-vue'
import skipFormatting from '@vue/eslint-config-prettier/skip-formatting'
import { readFileSync } from 'fs'

const autoImportConfig = JSON.parse(
    readFileSync('./.eslintrc-auto-import.json', 'utf-8')
)

export default defineConfig([
    // 全局忽略配置
    globalIgnores(['**/dist/**', '**/dist-ssr/**', '**/coverage/**']),

    // 基础JS配置
    js.configs.recommended,

    // Vue配置 (推荐使用 flat/recommended 除非你有特殊需求)
    ...pluginVue.configs['flat/recommended'], // 比 essential 更全面

    // Prettier跳过格式化
    skipFormatting,

    // 主应用配置
    {
        name: 'app/main-config',
        files: ['**/*.{js,mjs,jsx,vue}'],
        languageOptions: {
            globals: {
                ...globals.browser,
                ...globals.es2021, // 添加ES2021全局变量
                ...autoImportConfig.globals,
                process: 'readonly'
            },
            parserOptions: {
                ecmaVersion: 'latest',
                sourceType: 'module'
            }
        },
        rules: {
            'vue/multi-word-component-names': 'off',
            'vue/valid-template-root': 'off' // 或者 'off' 如果你不需要此规则
        }
    }
])
